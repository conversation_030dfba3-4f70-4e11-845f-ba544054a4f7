"use client";

import { useState } from "react";
import { Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2 } from "lucide-react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { InvoiceFile } from "../types";
import { Alert, AlertDescription } from "@/app/_component/Alert";
import { deleteInvoiceFile } from "../services/invoiceFileService";
import { CustomModal } from "@/app/_component/Modal";

interface DeleteInvoiceFileProps {
  file: InvoiceFile | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function DeleteInvoiceFile({
  file,
  open,
  onOpenChange,
  onSuccess,
}: DeleteInvoiceFileProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!file) return;

    setIsDeleting(true);
    try {
      await deleteInvoiceFile(file.id);
      console.log("Invoice file deleted successfully");
      onSuccess();
      onOpenChange(false);
    } catch (error) {
      toast.error("Failed to delete invoice file");
      console.error("Error deleting invoice file:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    if (!isDeleting) {
      onOpenChange(false);
    }
  };

  if (!file) return null;

  return (
    <>
      {/* Trigger button - you can replace or remove this */}
      {/* <Button onClick={() => onOpenChange(true)}>Delete File</Button> */}

      {/* Custom modal with your exact UI */}
      <CustomModal isOpen={open} onClose={handleClose}>
        {/* Original UI content starts here */}
        <div className="sm:max-w-[425px]">
          <div className="flex items-center space-x-2 mb-4">
            <Trash2 className="h-5 w-5 text-destructive" />
            <h2 className="text-lg font-semibold">Delete Invoice File</h2>
          </div>
          <p className="mb-4 text-center text-sm text-muted-foreground">
            Are you sure you want to delete the invoice file "
            <strong>{file.fileName}</strong>"? This action cannot be undone.
          </p>

          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You are about to delete the following invoice file:
            </AlertDescription>
          </Alert>

          <div className="bg-muted p-4 rounded-lg space-y-2 mt-4 mb-4">
            <div className="flex justify-between">
              <span className="font-medium">File Name:</span>
              <span>{file.fileName}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Date:</span>
              <span>{new Date(file.date).toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Pages:</span>
              <span>{file.noOfPages}</span>
            </div>
            {file.assignedToUser && (
              <div className="flex justify-between">
                <span className="font-medium">Assigned To:</span>
                <span>
                  {file.assignedToUser.firstName} {file.assignedToUser.lastName}
                </span>
              </div>
            )}
          </div>

          <Alert>
            <AlertDescription>
              <strong>Note:</strong> This is a soft delete operation. The file
              will be marked as deleted but can be restored by an administrator
              if needed.
            </AlertDescription>
          </Alert>

          {/* Buttons */}
          <div className="flex justify-end space-x-2 mt-4">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete File
            </Button>
          </div>
        </div>
      </CustomModal>
    </>
  );
}

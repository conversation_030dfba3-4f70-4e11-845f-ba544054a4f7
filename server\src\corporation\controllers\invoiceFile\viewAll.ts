import { handleError } from "../../../utils/helpers";

export const viewAllInvoiceFiles = async (req: any, res: any) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const skip = (page - 1) * limit;

    // Optional filters
    const { carrier, date, fileName, assignedTo } = req.query;

    const whereClause: any = {
      deletedAt: null, // Only return non-deleted records
    };

    // Add filters if provided
    if (carrier) {
      whereClause.carrier = {
        name: {
          contains: carrier as string,
          mode: "insensitive",
        },
      };
    }

    if (date) {
      whereClause.date = new Date(date as string);
    }
    if (fileName) {
      whereClause.fileName = {
        contains: fileName as string,
        mode: "insensitive",
      };
    }
    if (assignedTo) {
      whereClause.assignedTo = parseInt(assignedTo as string);
    }

    const [invoiceFiles, total] = await Promise.all([
      prisma.invoiceFile.findMany({
        where: whereClause,
        include: {
          carrier: {
            select: {
              name: true,
            },
          },
          assignedToUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip: skip,
        take: limit,
      }),
      prisma.invoiceFile.count({
        where: whereClause,
      }),
    ]);

    const invoiceFilesWithTickets = await Promise.all(
      invoiceFiles.map(async (file) => {
        const relatedTicket = await prisma.ticket.findFirst({
          where: {
            workItemId: file.id,
            deletedAt: null,
          },
          select: {
            id: true,
          },
        });

        return {
          ...file,
          ticketId: relatedTicket?.id || null,
        };
      })
    );

    const totalPages = Math.ceil(total / limit);

    return res.status(200).json({
      success: true,
      data: invoiceFilesWithTickets,
      pagination: {
        currentPage: page,
        totalPages: totalPages,
        totalRecords: total,
        limit: limit,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    return handleError(res, error);
  }
};

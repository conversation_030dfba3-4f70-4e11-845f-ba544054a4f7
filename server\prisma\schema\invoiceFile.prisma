enum InvoiceFileStatus {
  inProgress @map("in_progress")
  done
}

model InvoiceFile {
    id           String    @id @default(uuid())
    date         DateTime  @map("date") @db.Date
    fileName     String    @map("file_name") @db.VarChar(255)
    noOfPages    Int       @map("no_of_pages")
    assignedTo   Int       @map("assigned_to")
    carrierId    Int       @map("carrier_id")
    carrier      Carrier?  @relation(fields: [carrierId], references: [id], onDelete: Cascade)
    status       InvoiceFileStatus @default(inProgress) 
    createdAt    DateTime  @map("created_at") @default(now())
    createdBy    Int?      @map("created_by")
    updatedAt    DateTime? @map("updated_at") @updatedAt
    updatedBy    Int?      @map("updated_by")
    deletedAt    DateTime? @map("deleted_at")
    deletedBy    Int?      @map("deleted_by")

    // Foreign key relationships - only keep the primary relation
    assignedToUser User? @relation("InvoiceFileAssignedTo", fields: [assignedTo], references: [id], onDelete: Cascade)
    TrackSheets TrackSheets[]

    // Unique constraint on date + file_name combination
    @@unique([carrierId, noOfPages, date, fileName], map: "unique_carrier_id_no_of_pages_date_file_name")
    
    // Performance indexes
    @@index([assignedTo], map: "idx_invoice_files_assigned_to")
    @@index([createdAt], map: "idx_invoice_files_created_at")
    @@index([deletedAt], map: "idx_invoice_files_deleted_at")
    @@index([date], map: "idx_invoice_files_date")
    
    @@map("invoice_files")
}

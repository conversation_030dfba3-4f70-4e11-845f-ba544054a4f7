"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewAllInvoiceFiles = void 0;
const helpers_1 = require("../../../utils/helpers");
const viewAllInvoiceFiles = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        const skip = (page - 1) * limit;
        // Optional filters
        const { carrier, date, fileName, assignedTo } = req.query;
        const whereClause = {
            deletedAt: null, // Only return non-deleted records
        };
        // Add filters if provided
        if (carrier) {
            whereClause.carrier = {
                name: {
                    contains: carrier,
                    mode: "insensitive",
                },
            };
        }
        if (date) {
            whereClause.date = new Date(date);
        }
        if (fileName) {
            whereClause.fileName = {
                contains: fileName,
                mode: "insensitive",
            };
        }
        if (assignedTo) {
            whereClause.assignedTo = parseInt(assignedTo);
        }
        const [invoiceFiles, total] = await Promise.all([
            prisma.invoiceFile.findMany({
                where: whereClause,
                include: {
                    carrier: {
                        select: {
                            name: true,
                        },
                    },
                    assignedToUser: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: "desc",
                },
                skip: skip,
                take: limit,
            }),
            prisma.invoiceFile.count({
                where: whereClause,
            }),
        ]);
        const invoiceFilesWithTickets = await Promise.all(invoiceFiles.map(async (file) => {
            const relatedTicket = await prisma.ticket.findFirst({
                where: {
                    workItemId: file.id,
                    deletedAt: null,
                },
                select: {
                    id: true,
                },
            });
            return {
                ...file,
                ticketId: relatedTicket?.id || null,
            };
        }));
        const totalPages = Math.ceil(total / limit);
        return res.status(200).json({
            success: true,
            data: invoiceFilesWithTickets,
            pagination: {
                currentPage: page,
                totalPages: totalPages,
                totalRecords: total,
                limit: limit,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewAllInvoiceFiles = viewAllInvoiceFiles;
//# sourceMappingURL=viewAll.js.map
"use client";
import React, {
  useState,
  useCallback,
  useRef,
  useEffect,
  useMemo,
} from "react";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { getAllData } from "@/lib/helpers";
import {
  clientCustomFields_routes,
  trackSheets_routes,
  legrandMapping_routes,
  manualMatchingMapping_routes,
  customFilepath_routes,
  carrier_routes,
  invoiceFile_routes,
} from "@/lib/routePath";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";
import { TooltipProvider } from "@/components/ui/tooltip";
import ClientSelectPage from "../ClientSelectPage";
import TracksheetEntryForm from "./components/TracksheetEntryForm";
import { useTracksheetLogic } from "./hooks/useTracksheetLogic";
import { useFilenameGenerator } from "./hooks/useFilenameGenerator";
import { createTracksheetSubmit } from "./utils/createTracksheetSubmit";

const FIELD_OPTIONS = [
  "ASSOCIATE",
  "CLIENT",
  "ADDITIONALFOLDERNAME",
  "CARRIER",
  "YEAR",
  "MONTH",
  "RECEIVE DATE",
  "FTP FILE NAME",
];

const isField = (value: string) => FIELD_OPTIONS.includes(value);

const validateFtpPageFormat = (value: string): boolean => {
  if (!value || value.trim() === "") return false;

  const ftpPageRegex = /^(\d+)\s+of\s+(\d+)$/i;
  const match = value.match(ftpPageRegex);

  if (!match) return false;

  const currentPage = parseInt(match[1], 10);
  const totalPages = parseInt(match[2], 10);

  return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;
};

const validateDateFormat = (value: string): boolean => {
  if (!value || value.trim() === "") return true;

  const dateRegex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
  const match = value.match(dateRegex);

  if (!match) return false;

  const day = parseInt(match[1], 10);
  const month = parseInt(match[2], 10);
  const year = parseInt(match[3], 10);

  if (month < 1 || month > 12) return false;
  if (day < 1 || day > 31) return false;
  if (year < 1900 || year > 2100) return false;

  const date = new Date(year, month - 1, day);
  return (
    date.getFullYear() === year &&
    date.getMonth() === month - 1 &&
    date.getDate() === day
  );
};

const trackSheetSchema = z
  .object({
    clientId: z.string().min(1, "Client is required"),
    entries: z.array(
      z
        .object({
          company: z.string().min(1, "Company is required"),
          division: z.string().optional(),
          invoice: z.string().min(1, "Invoice is required"),
          masterInvoice: z.string().optional(),
          bol: z.string().optional(),
          invoiceDate: z
            .string()
            .min(1, "Invoice date is required")
            .refine(
              validateDateFormat,
              "Please enter a valid date in DD/MM/YYYY format"
            ),
          receivedDate: z
            .string()
            .min(1, "Received date is required")
            .refine(
              validateDateFormat,
              "Please enter a valid date in DD/MM/YYYY format"
            ),
          shipmentDate: z
            .string()
            .optional()
            .refine(
              (value) => !value || validateDateFormat(value),
              "Please enter a valid date in DD/MM/YYYY format"
            ),
          carrierName: z.string().min(1, "Carrier name is required"),
          invoiceStatus: z.string().min(1, "Invoice status is required"),
          manualMatching: z.string().min(1, "Manual matching is required"),
          invoiceType: z.string().min(1, "Invoice type is required"),
          billToClient: z.string().optional(),
          finalInvoice: z.boolean().optional(),
          currency: z.string().min(1, "Currency is required"),
          qtyShipped: z.string().optional(),
          weightUnitName: z.string().optional(),
          quantityBilledText: z.string().optional(),
          freightClass: z.string().optional(),
          invoiceTotal: z.string().min(1, "Invoice total is required"),
          savings: z.string().optional(),
          financialNotes: z.string().optional(),
          ftpFileName: z.string().min(1, "FTP File Name is required"),
          ftpPage: z
            .string()
            .min(1, "FTP Page is required")
            .refine(
              (value) => validateFtpPageFormat(value),
              (value) => {
                if (!value || value.trim() === "") {
                  return { message: "FTP Page is required" };
                }

                const ftpPageRegex = /^(\d+)\s+of\s+(\d+)$/i;
                const match = value.match(ftpPageRegex);

                if (!match) {
                  return { message: "" };
                }

                const currentPage = parseInt(match[1], 10);
                const totalPages = parseInt(match[2], 10);

                if (currentPage <= 0 || totalPages <= 0) {
                  return {
                    message: "Page numbers must be positive (greater than 0)",
                  };
                }

                if (currentPage > totalPages) {
                  return {
                    message: `Please enter a page number between ${totalPages} and ${currentPage} `,
                  };
                }

                return { message: "Invalid page format" };
              }
            ),
          docAvailable: z.array(z.string()).optional().default([]),
          otherDocuments: z.string().optional(),
          notes: z.string().optional(),
          legrandAlias: z.string().optional(),
          legrandCompanyName: z.string().optional(),
          legrandAddress: z.string().optional(),
          legrandZipcode: z.string().optional(),
          shipperAlias: z.string().optional(),
          shipperAddress: z.string().optional(),
          shipperZipcode: z.string().optional(),
          consigneeAlias: z.string().optional(),
          consigneeAddress: z.string().optional(),
          consigneeZipcode: z.string().optional(),
          billtoAlias: z.string().optional(),
          billtoAddress: z.string().optional(),
          billtoZipcode: z.string().optional(),
          shipperType: z.string().min(1, "DC/CV is required"),
          consigneeType: z.string().min(1, "DC/CV is required"),
          billtoType: z.string().min(1, "DC/CV is required"),
          legrandFreightTerms: z.string().min(1, "Freight Term is required"),
          customFields: z
            .array(
              z.object({
                id: z.string(),
                name: z.string(),
                type: z.string().optional(),
                value: z.string().optional(),
              })
            )
            .default([]),
          enteredBy: z.string().optional(),
        })
        .refine(
          (entry) => {
            if (
              validateDateFormat(entry.invoiceDate) &&
              validateDateFormat(entry.receivedDate)
            ) {
              const [invDay, invMonth, invYear] = entry.invoiceDate
                .split("/")
                .map(Number);
              const [recDay, recMonth, recYear] = entry.receivedDate
                .split("/")
                .map(Number);
              const invoiceDateObj = new Date(invYear, invMonth - 1, invDay);
              const receivedDateObj = new Date(recYear, recMonth - 1, recDay);
              return invoiceDateObj <= receivedDateObj;
            }
            return true;
          },
          {
            message:
              "The invoice date should be older than or the same as the received date.",
            path: ["invoiceDate"],
          }
        )
        .refine(
          (entry) => {
            if (
              entry.company === "LEGRAND" ||
              entry.company?.includes("LEGRAND")
            ) {
              return (
                entry.legrandFreightTerms &&
                entry.legrandFreightTerms.trim() !== ""
              );
            }
            return true;
          },
          {
            message: "Freight Term is required for LEGRAND clients.",
            path: ["legrandFreightTerms"],
          }
        )
        .refine(
          (entry) => {
            if (
              entry.company === "LEGRAND" ||
              entry.company?.includes("LEGRAND")
            ) {
              return (
                entry.shipperType && entry.consigneeType && entry.billtoType
              );
            }
            return true;
          },
          {
            message: "DC/CV selection is required for all Legrand blocks.",
            path: ["shipperType"],
          }
        )
    ),
  })
  .refine(
    (data) => {
      for (let i = 0; i < data.entries.length; i++) {
        const entry = data.entries[i];
        if (entry.company === "LEGRAND" || entry.company?.includes("LEGRAND")) {
          if (
            !entry.freightClass ||
            entry.freightClass.trim() === "" ||
            !entry.shipperType ||
            !entry.consigneeType ||
            !entry.billtoType ||
            !entry.legrandFreightTerms
          ) {
            return false;
          }
        }
      }
      return true;
    },
    {
      message:
        "Billing type and DC/CV selections are required for all Legrand blocks.",
      path: ["entries"],
    }
  );

const CreateTrackSheet = ({
  client,
  associate,
  userData,
  activeView,
  setActiveView,
  permissions,
  carrierDataUpdate,
  clientDataUpdate,
  carrier,
  associateId,
  clientId,
  legrandsData,
}: any) => {
  const userName = userData?.username;
  const companyFieldRefs = useRef<(HTMLElement | null)[]>([]);
  const [isImportModalOpen, setImportModalOpen] = useState(false);
  const [clientFilePathFormat, setClientFilePathFormat] = useState<
    string | null
  >(null);
  const [existingEntries, setExistingEntries] = useState<{
    [key: string]: boolean;
  }>({});

  const [generatedFilenames, setGeneratedFilenames] = useState<string[]>([]);
  const [filenameValidation, setFilenameValidation] = useState<boolean[]>([]);
  const [missingFields, setMissingFields] = useState<string[][]>([]);
  const [legrandData, setLegrandData] = useState<any[]>([]);
  const [manualMatchingData, setManualMatchingData] = useState<any[]>([]);
  const [customFieldsRefresh, setCustomFieldsRefresh] = useState<number>(0);
  const [showFullForm, setShowFullForm] = useState(false);
  const [assignedFiles, setAssignedFiles] = useState([]);

  const [carrierByClient, setCarrierByClient] = useState([]);

  const handleChange = async (id: string) => {
    try {
      const carrierByClientData = await getAllData(
        `${carrier_routes.GET_CARRIER_BY_CLIENT}/${id}`
      );
      if (carrierByClientData && Array.isArray(carrierByClientData)) {
        const formattedCarriers = carrierByClientData
          .map((item: any) => ({
            value: item.carrier?.id?.toString(),
            label: item.carrier?.name,
          }))
          .filter((carrier: any) => carrier.value && carrier.label);

        formattedCarriers.sort((a: any, b: any) =>
          a.label.localeCompare(b.label)
        );

        setCarrierByClient(formattedCarriers);
      } else {
        setCarrierByClient([]);
      }
    } catch (error) {
      console.error("Error fetching carrier data:", error);
      setCarrierByClient([]);
    }
  };

  const router = useRouter();

  const form = useForm({
    resolver: zodResolver(trackSheetSchema),
    defaultValues: {
      associateId: associateId || "",
      clientId: clientId || "",
      entries: [
        {
          company: "",
          division: "",
          invoice: "",
          masterInvoice: "",
          bol: "",
          invoiceDate: "",
          receivedDate: "",
          shipmentDate: "",
          carrierName: "",
          invoiceStatus: "ENTRY",
          manualMatching: "",
          invoiceType: "",
          billToClient: "yes",
          finalInvoice: false,
          currency: "",
          qtyShipped: "",
          weightUnitName: "",
          quantityBilledText: "",
          freightClass: "",
          invoiceTotal: "",
          savings: "",
          financialNotes: "",
          ftpFileName: "",
          ftpPage: "",
          docAvailable: [],
          otherDocuments: "",
          notes: "",
          legrandAlias: "",
          legrandCompanyName: "",
          legrandAddress: "",
          legrandZipcode: "",
          shipperAlias: "",
          shipperAddress: "",
          shipperZipcode: "",
          consigneeAlias: "",
          consigneeAddress: "",
          consigneeZipcode: "",
          billtoAlias: "",
          billtoAddress: "",
          billtoZipcode: "",
          shipperType: "",
          consigneeType: "",
          billtoType: "",
          legrandFreightTerms: "",
          customFields: [],
          enteredBy: userData?.username || "",
        },
      ],
    },
  });

  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      if (name?.includes("receivedDate") || name?.includes("ftpFileName")) {
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const handleDateChange = useCallback(
    (index: number, value: string) => {
      form.setValue(`entries.${index}.receivedDate`, value, {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
    },
    [form]
  );

  const handleFtpFileNameChange = useCallback(
    (index: number, value: string) => {
      form.setValue(`entries.${index}.ftpFileName`, value, {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
    },
    [form]
  );

  const clientOptions = useMemo(() => {
    if (!associateId) {
      return (
        client?.map((c: any) => ({
          value: c.id?.toString(),
          label: c.client_name,
          name: c.client_name,
        })) || []
      );
    }
    const filteredClients =
      client?.filter((c: any) => c.associateId?.toString() === associateId) ||
      [];

    return filteredClients.map((c: any) => ({
      value: c.id?.toString(),
      label: c.client_name,
      name: c.client_name,
    }));
  }, [client, associateId]);

  const entries = useWatch({ control: form.control, name: "entries" });
  const watchedClientId = useWatch({ control: form.control, name: "clientId" });

  const validateClientForAssociate = useCallback(
    (associateId: string, currentClientId: string) => {
      if (associateId && currentClientId) {
        const currentClient = client?.find(
          (c: any) => c.id?.toString() === currentClientId
        );
        if (
          currentClient &&
          currentClient.associateId?.toString() !== associateId
        ) {
          form.setValue("clientId", "");
          return false;
        }
      }
      return true;
    },
    [client, form]
  );

  const clearEntrySpecificClients = useCallback(() => {
    const currentEntries = form.getValues("entries") || [];
    if (currentEntries.length > 0) {
      const hasEntrySpecificClients = currentEntries.some(
        (entry: any) => entry.clientId
      );
      if (hasEntrySpecificClients) {
        const updatedEntries = currentEntries.map((entry: any) => ({
          ...entry,
          clientId: "",
        }));
        form.setValue("entries", updatedEntries);
      }
    }
  }, [form]);

  const fetchClientFilePathFormat = async (clientId: string) => {
    try {
      const response = await fetch(
        `${customFilepath_routes.GET_CLIENT_CUSTOM_FILEPATH}?clientId=${clientId}`
      );
      if (response.ok) {
        const result = await response.json();
        if (
          result.success &&
          result.data &&
          Array.isArray(result.data) &&
          result.data.length > 0
        ) {
          const filepathData = result.data[0];
          if (filepathData && filepathData.filePath) {
            setClientFilePathFormat(filepathData.filePath);
            // setTimeout(() => updateFilenames(), 0); // REMOVE THIS
          } else {
            setClientFilePathFormat(null);
          }
        } else {
          setClientFilePathFormat(null);
        }
      } else {
        setClientFilePathFormat(null);
      }
    } catch (error) {
      console.error(
        "[fetchClientFilePathFormat] Error fetching filePath for client:",
        error
      );
      setClientFilePathFormat(null);
    }
  };

  const fetchLegrandData = useCallback(async () => {
    try {
      const response = await getAllData(
        legrandMapping_routes.GET_LEGRAND_MAPPINGS
      );
      if (response && Array.isArray(response.data)) {
        setLegrandData(response.data);
      } else {
        setLegrandData([]);
      }
    } catch (error) {
      toast.error("Error fetching LEGRAND mapping data:", error);
      setLegrandData([]);
    }
  }, []);

  const fetchManualMatchingData = useCallback(async () => {
    try {
      const response = await getAllData(
        manualMatchingMapping_routes.GET_MANUAL_MATCHING_MAPPINGS
      );
      if (response && Array.isArray(response)) {
        setManualMatchingData(response);
      }
    } catch (error) {
      toast.error("Error fetching manual matching mapping data:", error);
    }
  }, []);

  useEffect(() => {
    if (!watchedClientId) {
      setLegrandData([]);
      setManualMatchingData([]);
      return;
    }
    const selectedClient = clientOptions.find(
      (c: any) => c.value === watchedClientId
    );
    if (
      selectedClient &&
      selectedClient.name.toLowerCase().includes("legrand")
    ) {
      fetchLegrandData();
      fetchManualMatchingData();
    } else {
      setLegrandData([]);
      setManualMatchingData([]);
    }
  }, [
    watchedClientId,
    clientOptions,
    fetchLegrandData,
    fetchManualMatchingData,
  ]);

  const handleLegrandDataChange = (
    entryIndex: number,
    businessUnit: string,
    divisionCode: string
  ) => {
    form.setValue(`entries.${entryIndex}.company`, businessUnit);

    if (divisionCode) {
      form.setValue(`entries.${entryIndex}.division`, divisionCode);
      handleManualMatchingAutoFill(entryIndex, divisionCode);
    } else {
      form.setValue(`entries.${entryIndex}.division`, "");
      form.setValue(`entries.${entryIndex}.manualMatching`, "");
    }
  };

  const handleManualMatchingAutoFill = useCallback(
    (entryIndex: number, division: string) => {
      if (!division || !manualMatchingData.length) {
        return;
      }

      const formValues = form.getValues();
      const entry = formValues.entries?.[entryIndex] as any;
      const entryClientId =
        entry?.clientId || (entryIndex === 0 ? formValues.clientId : "");
      const entryClientName =
        clientOptions?.find((c: any) => c.value === entryClientId)?.name || "";

      if (entryClientName !== "LEGRAND") {
        return;
      }

      const matchingEntry = manualMatchingData.find(
        (mapping: any) => mapping.division === division
      );

      if (matchingEntry && matchingEntry.ManualShipment) {
        form.setValue(
          `entries.${entryIndex}.manualMatching`,
          matchingEntry.ManualShipment
        );
      } else {
        form.setValue(`entries.${entryIndex}.manualMatching`, "");
      }
    },
    [form, manualMatchingData, clientOptions]
  );

  const fetchCustomFieldsForClient = useCallback(
    async (clientId: string) => {
      if (!clientId) return [];

      try {
        const allCustomFieldsResponse = await getAllData(
          `${clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS}/${clientId}`
        );

        let customFieldsData: any[] = [];
        if (
          allCustomFieldsResponse &&
          allCustomFieldsResponse.custom_fields &&
          allCustomFieldsResponse.custom_fields.length > 0
        ) {
          customFieldsData = allCustomFieldsResponse.custom_fields.map(
            (field: any) => {
              let autoFilledValue = "";

              if (field.type === "AUTO") {
                if (field.autoOption === "DATE") {
                  const today = new Date();
                  const day = today.getDate().toString().padStart(2, "0");
                  const month = (today.getMonth() + 1)
                    .toString()
                    .padStart(2, "0");
                  const year = today.getFullYear();
                  autoFilledValue = `${day}/${month}/${year}`;
                } else if (field.autoOption === "USERNAME") {
                  autoFilledValue = userData?.username || "";
                }
              }

              return {
                id: field.id,
                name: field.name,
                type: field.type,
                autoOption: field.autoOption,
                value: autoFilledValue,
              };
            }
          );
        }

        return customFieldsData;
      } catch (error) {
        return [];
      }
    },
    [userData]
  );

  const fields = [{ id: "single-entry" }];
  useEffect(() => {
    if (watchedClientId) {
      fetchClientFilePathFormat(watchedClientId);
      handleChange(watchedClientId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watchedClientId]);

  useEffect(() => {
    companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);
  }, [fields.length]);

  const { generateFilename } = useFilenameGenerator({
    associate,
    client,
    carrier,
    userName,
    associateId: associateId || "",
  });

  const updateFilenames = useCallback(() => {
    const formValues = form.getValues();
    const filenames: string[] = [];
    const validations: boolean[] = [];
    const missingArr: string[][] = [];

    if (!clientFilePathFormat) {
      return;
    }

    if (formValues.entries && Array.isArray(formValues.entries)) {
      formValues.entries.forEach((entry, index) => {
        const { filename, isValid, missing, debug } = generateFilename(
          index,
          formValues,
          clientFilePathFormat // <-- pass as argument
        );
        filenames[index] = filename;
        validations[index] = isValid;
        missingArr[index] = missing || [];
      });
    }
    setGeneratedFilenames(filenames);
    setFilenameValidation(validations);
    setMissingFields(missingArr);
  }, [form, generateFilename, clientFilePathFormat]);

  // Add this effect to call updateFilenames when clientFilePathFormat changes
  useEffect(() => {
    if (clientFilePathFormat) {
      updateFilenames();
    }
  }, [clientFilePathFormat, updateFilenames]);

  // Use hooks for dynamic logic
  useTracksheetLogic({
    form,
    clientOptions, // define as in your file
    legrandData,
    setCarrierByClient,
    carrier,
    associate,
    client,
    setClientFilePathFormat,
    clientFilePathMap: {}, // define as needed
    updateFilenames,
    handleManualMatchingAutoFill,
    handleLegrandDataChange,
  });

  useEffect(() => {
    if (clientFilePathFormat && entries && Array.isArray(entries)) {
      updateFilenames();
    }
  }, [clientFilePathFormat, entries, updateFilenames]);

  const handleCompanyAutoPopulation = useCallback(
    (entryIndex: number, entryClientId: string) => {
      const entryClientName =
        clientOptions?.find((c: any) => c.value === entryClientId)?.name || "";
      const currentEntry = form.getValues(`entries.${entryIndex}`);
      if (entryClientName && entryClientName !== "LEGRAND") {
        form.setValue(`entries.${entryIndex}.company`, entryClientName);
      } else if (entryClientName === "LEGRAND") {
        const shipperAlias = (currentEntry as any).shipperAlias;
        const consigneeAlias = (currentEntry as any).consigneeAlias;
        const billtoAlias = (currentEntry as any).billtoAlias;
        const hasAnyLegrandData = shipperAlias || consigneeAlias || billtoAlias;
        if (!hasAnyLegrandData && currentEntry.company !== "") {
          form.setValue(`entries.${entryIndex}.company`, "");
        }
      } else {
        if (currentEntry.company !== "") {
          form.setValue(`entries.${entryIndex}.company`, "");
        }
      }
    },
    [form, clientOptions]
  );

  const handleCustomFieldsFetch = useCallback(
    async (entryIndex: number, entryClientId: string) => {
      if (!entryClientId) {
        const currentCustomFields = form.getValues(
          `entries.${entryIndex}.customFields`
        );
        if (currentCustomFields && currentCustomFields.length > 0) {
          form.setValue(`entries.${entryIndex}.customFields`, []);
        }
        return;
      }
      const currentCustomFields =
        form.getValues(`entries.${entryIndex}.customFields`) || [];
      const hasEmptyAutoUsernameFields = currentCustomFields.some(
        (field: any) =>
          field.type === "AUTO" &&
          field.autoOption === "USERNAME" &&
          !field.value &&
          userData?.username
      );
      const shouldFetchCustomFields =
        currentCustomFields.length === 0 ||
        (currentCustomFields.length > 0 && !currentCustomFields[0]?.clientId) ||
        currentCustomFields[0]?.clientId !== entryClientId ||
        hasEmptyAutoUsernameFields;
      if (shouldFetchCustomFields) {
        const customFieldsData = await fetchCustomFieldsForClient(
          entryClientId
        );
        const fieldsWithClientId = customFieldsData.map((field: any) => ({
          ...field,
          clientId: entryClientId,
        }));
        form.setValue(`entries.${entryIndex}.customFields`, fieldsWithClientId);
        setTimeout(() => {
          fieldsWithClientId.forEach((field: any, fieldIndex: number) => {
            const fieldPath =
              `entries.${entryIndex}.customFields.${fieldIndex}.value` as any;
            if (field.value) {
              form.setValue(fieldPath, field.value);
            }
          });
          setCustomFieldsRefresh((prev) => prev + 1);
        }, 100);
      }
    },
    [form, fetchCustomFieldsForClient, userData?.username]
  );

  const handleInitialSelection = useCallback(
    (_associateId: string, _clientId: string) => {
      form.setValue("associateId", associateId);
      form.setValue("clientId", clientId);

      setTimeout(() => {
        handleCompanyAutoPopulation(0, clientId);
        handleCustomFieldsFetch(0, clientId);
      }, 50);

      setShowFullForm(true);
    },
    [
      form,
      handleCompanyAutoPopulation,
      handleCustomFieldsFetch,
      associateId,
      clientId,
    ]
  );

  useEffect(() => {
    if (associateId && clientId) {
      setShowFullForm(true);
      handleInitialSelection(associateId, clientId);
    } else {
      setShowFullForm(false);
    }
  }, [associateId, clientId, handleInitialSelection]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateFilenames();
      const formValues = form.getValues();
      if (formValues.entries && Array.isArray(formValues.entries)) {
        formValues.entries.forEach((entry: any, index: number) => {
          const entryClientId =
            entry?.clientId || (index === 0 ? formValues.clientId : "");
          if (entryClientId) {
          }
        });
      }
    }, 50);

    return () => clearTimeout(timeoutId);
  }, [updateFilenames, handleCustomFieldsFetch, form]);

  useEffect(() => {
    const subscription = form.watch((_, { name }) => {
      if (
        name &&
        (name.includes("associateId") ||
          name.includes("clientId") ||
          name.includes("carrierName") ||
          name.includes("invoiceDate") ||
          name.includes("receivedDate") ||
          name.includes("ftpFileName") ||
          name.includes("company") ||
          name.includes("division"))
      ) {
        const timeoutId = setTimeout(() => {
          updateFilenames();

          if (name.includes("division")) {
            const entryMatch = name.match(/entries\.(\d+)\.division/);
            if (entryMatch) {
              const entryIndex = parseInt(entryMatch[1], 10);
              const formValues = form.getValues();
              const entry = formValues.entries?.[entryIndex] as any;
              const entryClientId =
                entry?.clientId ||
                (entryIndex === 0 ? formValues.clientId : "");
              const entryClientName =
                clientOptions?.find((c: any) => c.value === entryClientId)
                  ?.name || "";

              if (entryClientName === "LEGRAND") {
                const divisionValue =
                  formValues.entries?.[entryIndex]?.division;
                if (divisionValue) {
                  handleManualMatchingAutoFill(entryIndex, divisionValue);
                }
              }
            }
          }

          if (name.includes("clientId")) {
            const entryMatch = name.match(/entries\.(\d+)\.clientId/);
            if (entryMatch) {
              const entryIndex = parseInt(entryMatch[1], 10);
              const formValues = form.getValues();
              const entry = formValues.entries?.[entryIndex] as any;
              const entryClientId =
                entry?.clientId || formValues.clientId || "";
              const entryClientName =
                clientOptions?.find((c: any) => c.value === entryClientId)
                  ?.name || "";

              if (entryClientName === "LEGRAND" && entry?.division) {
                handleManualMatchingAutoFill(entryIndex, entry.division);
              }
            }
          }
        }, 100);

        return () => clearTimeout(timeoutId);
      }
    });

    return () => subscription.unsubscribe();
  }, [updateFilenames, handleManualMatchingAutoFill, clientOptions, form]);

  useEffect(() => {
    if (associateId) {
      form.setValue("associateId", associateId);
    }
  }, [associateId, form]);

  const checkInvoiceExistence = useCallback(async (invoice: string) => {
    if (!invoice || invoice.length < 3) return false;
    try {
      const response = await getAllData(
        `${trackSheets_routes.GET_RECEIVED_DATES_BY_INVOICE}?invoice=${invoice}`
      );
      console.log(
        "[checkInvoiceExistence] invoice:",
        invoice,
        "API response:",
        response
      );
      return Array.isArray(response) && response.length > 0;
    } catch (error) {
      console.error("[checkInvoiceExistence] Error:", error);
      return false;
    }
  }, []);

  const checkReceivedDateExistence = useCallback(
    async (invoice: string, receivedDate: string) => {
      if (!invoice || !receivedDate || invoice.length < 3) return false;
      try {
        const response = await getAllData(
          `${trackSheets_routes.GET_RECEIVED_DATES_BY_INVOICE}?invoice=${invoice}`
        );
        console.log(
          "[checkReceivedDateExistence] invoice:",
          invoice,
          "receivedDate:",
          receivedDate,
          "API response:",
          response
        );
        if (Array.isArray(response) && response.length > 0) {
          const [day, month, year] = receivedDate.split("/");
          const inputDate = new Date(
            Date.UTC(
              parseInt(year, 10),
              parseInt(month, 10) - 1,
              parseInt(day, 10)
            )
          );
          const inputDateISO = inputDate.toISOString().split("T")[0];

          const exists = response.some((item: any) => {
            if (item.receivedDate) {
              const apiDate = new Date(item.receivedDate);
              const apiDateISO = apiDate.toISOString().split("T")[0];
              return apiDateISO === inputDateISO;
            }
            return false;
          });
          console.log("[checkReceivedDateExistence] exists:", exists);
          setExistingEntries((prev) => ({
            ...prev,
            [`${invoice}-${receivedDate}`]: exists,
          }));

          return exists;
        }
        return false;
      } catch (error) {
        console.error("Error checking received date:", error);
        return false;
      }
    },
    []
  );

  const onSubmit = useCallback(
    async (values: any, event?: any) => {
      // Clear previous errors
      for (let i = 0; i < values.entries.length; i++) {
        form.clearErrors(`entries.${i}.invoice`);
        form.clearErrors(`entries.${i}.receivedDate`);
      }

      // Async duplicate checks for each entry
      const errorsToApply: { field: string; message: string }[] = [];
      const validationPromises = values.entries.map(
        async (entry: any, index: number) => {
          let entryHasError = false;
          const invoiceExists = await checkInvoiceExistence(entry.invoice);
          if (invoiceExists) {
            errorsToApply.push({
              field: `entries.${index}.invoice`,
              message: "This invoice already exists",
            });
            if (entry.invoice && entry.receivedDate) {
              const receivedDateExists = await checkReceivedDateExistence(
                entry.invoice,
                entry.receivedDate
              );
              if (receivedDateExists) {
                errorsToApply.push({
                  field: `entries.${index}.receivedDate`,
                  message: "This received date already exists for this invoice",
                });
                entryHasError = true;
              } else {
                errorsToApply.push({
                  field: `entries.${index}.receivedDate`,
                  message:
                    "Warning: Different received date for existing invoice",
                });
              }
            }
          }
          return { isValid: !entryHasError };
        }
      );

      await Promise.all(validationPromises);

      errorsToApply.forEach(({ field, message }) => {
        form.setError(field as any, { type: "manual", message });
      });

      const hasDuplicateReceivedDateErrors = errorsToApply.some(
        (error) =>
          error.field.includes("receivedDate") &&
          error.message.includes("already exists")
      );

      if (hasDuplicateReceivedDateErrors) {
        return;
      }

      // If all checks pass, proceed to submit
      await createTracksheetSubmit({
        values,
        form,
        clientFilePathFormat,
        generateFilename,
        notify: (type: any, message: any) => toast[type](message),
        onSuccess: () => {
          form.setValue("associateId", associateId);
        },
        userData,
      });
    },
    [
      form,
      clientFilePathFormat,
      generateFilename,
      associateId,
      checkInvoiceExistence,
      checkReceivedDateExistence,
      userData,
    ]
  );

  const handleFormKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.ctrlKey && (e.key === "s" || e.key === "S")) {
        e.preventDefault();
        form.handleSubmit(onSubmit)();
      } else if (e.key === "Enter" && !e.ctrlKey && !e.shiftKey && !e.altKey) {
        const activeElement = document.activeElement;
        const isSubmitButton = activeElement?.getAttribute("type") === "submit";

        if (isSubmitButton) {
          e.preventDefault();
          form.handleSubmit(onSubmit)();
        }
      }
    },
    [form, onSubmit]
  );

  const getFilteredDivisionOptions = (company: string, entryIndex?: number) => {
    if (entryIndex !== undefined) {
      const formValues = form.getValues();
      const entry = formValues.entries?.[entryIndex] as any;
      const entryClientId =
        entry?.clientId || (entryIndex === 0 ? formValues.clientId : "");
      const entryClientName =
        clientOptions?.find((c: any) => c.value === entryClientId)?.name || "";

      if (entryClientName === "LEGRAND") {
        const shipperAlias = form.getValues(
          `entries.${entryIndex}.shipperAlias`
        );
        const consigneeAlias = form.getValues(
          `entries.${entryIndex}.consigneeAlias`
        );
        const billtoAlias = form.getValues(`entries.${entryIndex}.billtoAlias`);

        const currentAlias = shipperAlias || consigneeAlias || billtoAlias;

        if (currentAlias) {
          const selectedData = legrandData.find((data) => {
            const uniqueKey = `${data.customeCode}-${
              data.aliasShippingNames || data.legalName
            }-${data.shippingBillingAddress}`;
            return uniqueKey === currentAlias;
          });

          if (selectedData) {
            const baseAliasName =
              selectedData.aliasShippingNames &&
              selectedData.aliasShippingNames !== "NONE"
                ? selectedData.aliasShippingNames
                : selectedData.legalName;

            const sameAliasEntries = legrandData.filter((data) => {
              const dataAliasName =
                data.aliasShippingNames && data.aliasShippingNames !== "NONE"
                  ? data.aliasShippingNames
                  : data.legalName;
              return dataAliasName === baseAliasName;
            });

            const allDivisions: string[] = [];
            sameAliasEntries.forEach((entry) => {
              if (entry.customeCode) {
                if (entry.customeCode.includes("/")) {
                  const splitDivisions = entry.customeCode
                    .split("/")
                    .map((d: string) => d.trim());
                  allDivisions.push(...splitDivisions);
                } else {
                  allDivisions.push(entry.customeCode);
                }
              }
            });

            const uniqueDivisions = Array.from(
              new Set(allDivisions.filter((code) => code))
            );

            if (uniqueDivisions.length > 0) {
              const contextDivisions = uniqueDivisions.sort().map((code) => ({
                value: code,
                label: code,
              }));

              return contextDivisions;
            }
          }
        }
      }
    }
    return [];
  };

  const handleOpenImportModal = () => {
    setImportModalOpen(true);
  };

  const handleCloseImportModal = () => {
    setImportModalOpen(false);
  };

  const renderTooltipContent = (index: number) => {
    if (!clientFilePathFormat) {
      return (
        <div className="text-sm max-w-md">
          <p className="font-medium mb-1">Entry #{index + 1} Filename</p>
          <p className="font-medium text-orange-600 mb-2">
            Please select a client to generate filename
          </p>
        </div>
      );
    }

    const hasGeneratedFilename =
      generatedFilenames[index] && generatedFilenames[index].length > 0;
    const isValid = filenameValidation[index];

    return (
      <div className="text-sm max-w-md">
        <p className="font-medium mb-1">Entry #{index + 1} Filename</p>
        {hasGeneratedFilename && isValid ? (
          <div>
            <p className="font-medium text-green-600 mb-2">
              Filename Generated Successfully
            </p>
            <p className="text-xs font-mono break-all bg-gray-100 p-2 rounded text-black">
              {generatedFilenames[index]}
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Pattern: {clientFilePathFormat}
            </p>
          </div>
        ) : (
          <div>
            <p className="font-medium text-orange-600 mb-1">
              {hasGeneratedFilename
                ? "Invalid Filename"
                : "Please fill the form to generate filename"}
            </p>
            {missingFields[index] && missingFields[index].length > 0 && (
              <>
                <p className="text-xs text-gray-600 mb-2">Missing fields:</p>
                <ul className="list-disc list-inside space-y-1">
                  {missingFields[index].map((field, fieldIndex) => (
                    <li key={fieldIndex} className="text-xs">
                      {field}
                    </li>
                  ))}
                </ul>
              </>
            )}
          </div>
        )}
      </div>
    );
  };

  useEffect(() => {
    const fetchAssignedFiles = async () => {
      if (!userData?.id) return;
      try {
        const res = await fetch(
          `${invoiceFile_routes.GET_INVOICE_FILES_BY_USER}/${userData.id}`
        );
        const data = await res.json();
        console.log(data);
        
        if (data.success && Array.isArray(data.data)) {
          setAssignedFiles(data.data);
        } else {
          setAssignedFiles([]);
        }
      } catch (err) {
        setAssignedFiles([]);
      }
    };
    fetchAssignedFiles();
  }, [userData?.id]);

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="w-full px-2 py-3">
          {activeView === "view" ? (
            <div className="w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1">
              <ClientSelectPage
                permissions={permissions}
                client={client}
                clientDataUpdate={clientDataUpdate}
                carrierDataUpdate={carrierDataUpdate}
              />
            </div>
          ) : (
            showFullForm && (
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-3"
                >
                  {entries.map((entry: any, index: number) => (
                    <TracksheetEntryForm
                      key={index}
                      index={index}
                      form={form}
                      clientOptions={clientOptions}
                      carrierByClient={carrierByClient}
                      legrandData={legrandData}
                      handleFtpFileNameChange={handleFtpFileNameChange}
                      handleLegrandDataChange={handleLegrandDataChange}
                      getFilteredDivisionOptions={getFilteredDivisionOptions}
                      updateFilenames={updateFilenames}
                      clientFilePathFormat={clientFilePathFormat}
                      generatedFilenames={generatedFilenames}
                      filenameValidation={filenameValidation}
                      renderTooltipContent={renderTooltipContent}
                      checkInvoiceExistence={checkInvoiceExistence}
                      checkReceivedDateExistence={checkReceivedDateExistence}
                      validateDateFormat={validateDateFormat}
                      handleDateChange={handleDateChange}
                      legrandsData={legrandsData}
                      manualMatchingData={manualMatchingData}
                      handleManualMatchingAutoFill={
                        handleManualMatchingAutoFill
                      }
                      assignedFiles={assignedFiles}
                    />
                  ))}
                  {/* Submit Button */}
                  <div className="flex justify-end pt-2">
                    <Button type="submit" className="w-32">
                      Save
                    </Button>
                  </div>
                </form>
              </Form>
            )
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

export default CreateTrackSheet;

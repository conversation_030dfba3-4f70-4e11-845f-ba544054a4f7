{"version": 3, "file": "distribution.js", "sourceRoot": "", "sources": ["../../../../../src/corporation/controllers/ticket/analytics/distribution.ts"], "names": [], "mappings": ";;;AAAA,uDAAwD;AAExD;;;;;;;GAOG;AACI,MAAM,4BAA4B,GAAG,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IACvE,IAAI,CAAC;QACH,MAAM,EACJ,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,UAAU,EACV,OAAO,EACP,QAAQ,GACT,GAAG,GAAG,CAAC,KAAK,CAAC;QAEd,iCAAiC;QACjC,MAAM,iBAAiB,GAAQ;YAC7B,SAAS,EAAE,IAAI;SAChB,CAAC;QAEF,qBAAqB;QACrB,IAAI,QAAQ,IAAI,MAAM,EAAE,CAAC;YACvB,iBAAiB,CAAC,SAAS,GAAG,EAAE,CAAC;YACjC,IAAI,QAAQ,EAAE,CAAC;gBACb,iBAAiB,CAAC,SAAS,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvD,CAAC;YACD,IAAI,MAAM,EAAE,CAAC;gBACX,iBAAiB,CAAC,SAAS,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YAClE,iBAAiB,CAAC,IAAI,GAAG;gBACvB,OAAO,EAAE,QAAQ;aAClB,CAAC;QACJ,CAAC;QAED,yBAAyB;QACzB,IAAI,QAAQ,EAAE,CAAC;YACb,iBAAiB,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACxC,CAAC;QAED,uCAAuC;QACvC,MAAM,gBAAgB,GAAQ,EAAE,CAAC;QACjC,IAAI,UAAU,EAAE,CAAC;YACf,gBAAgB,CAAC,UAAU,GAAG,UAAU,CAAC;QAC3C,CAAC;QACD,IAAI,OAAO,EAAE,CAAC;YACZ,gBAAgB,CAAC,eAAe,GAAG,OAAO,CAAC;QAC7C,CAAC;QAED,4CAA4C;QAC5C,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC3C,KAAK,EAAE;gBACL,GAAG,iBAAiB;gBACpB,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI;oBAC9C,MAAM,EAAE;wBACN,IAAI,EAAE,gBAAgB;qBACvB;iBACF,CAAC;aACH;YACD,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,OAAO,EAAE;wBACP,aAAa,EAAE,IAAI;qBACpB;oBACD,OAAO,EAAE;wBACP,SAAS,EAAE,MAAM;qBAClB;oBACD,IAAI,EAAE,CAAC,EAAE,4BAA4B;iBACtC;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;yBAC1B;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,mDAAmD;QACnD,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE3F,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;YACpD,KAAK,EAAE;gBACL,UAAU,EAAE;oBACV,EAAE,EAAE,WAAW;iBAChB;gBACD,SAAS,EAAE,IAAI;aAChB;YACD,OAAO,EAAE;gBACP,EAAE,UAAU,EAAE,KAAK,EAAE;gBACrB,EAAE,KAAK,EAAE,KAAK,EAAE;aACjB;SACF,CAAC,CAAC;QAEH,0CAA0C;QAC1C,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAM7B,CAAC;QAEL,sCAAsC;QACtC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE;gBAC9B,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,SAAS,EAAE,KAAK,CAAC,IAAI,IAAI,eAAe;gBACxC,UAAU,EAAE,KAAK,CAAC,KAAK;gBACvB,UAAU,EAAE,KAAK,CAAC,UAAU,IAAI,EAAE;gBAClC,WAAW,EAAE,CAAC;aACf,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,gDAAgD;QAChD,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,IAAI,MAAM,CAAC,cAAc,IAAI,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC1E,MAAM,SAAS,GAAG,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,cAAc,CAAE,CAAC;gBAChE,SAAS,CAAC,WAAW,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,iDAAiD;QACjD,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;QACpC,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC7E,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,UAAU,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,WAAW,GAAG,YAAY,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;SAChG,CAAC,CAAC,CAAC;QAEJ,sBAAsB;QACtB,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;QAE9D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,iBAAiB;SACxB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAC/D,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAhJW,QAAA,4BAA4B,gCAgJvC"}
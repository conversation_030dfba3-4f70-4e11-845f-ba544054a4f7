"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/crc32-stream";
exports.ids = ["vendor-chunks/crc32-stream"];
exports.modules = {

/***/ "(ssr)/./node_modules/crc32-stream/lib/crc32-stream.js":
/*!*******************************************************!*\
  !*** ./node_modules/crc32-stream/lib/crc32-stream.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-crc32-stream\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-crc32-stream/blob/master/LICENSE-MIT\n */\n\n \n\nconst {Transform} = __webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\");\n\nconst crc32 = __webpack_require__(/*! crc-32 */ \"(ssr)/./node_modules/crc-32/crc32.js\");\n\nclass CRC32Stream extends Transform {\n  constructor(options) {\n    super(options);\n    this.checksum = Buffer.allocUnsafe(4);\n    this.checksum.writeInt32BE(0, 0);\n\n    this.rawSize = 0;\n  }\n\n  _transform(chunk, encoding, callback) {\n    if (chunk) {\n      this.checksum = crc32.buf(chunk, this.checksum) >>> 0;\n      this.rawSize += chunk.length;\n    }\n\n    callback(null, chunk);\n  }\n\n  digest(encoding) {\n    const checksum = Buffer.allocUnsafe(4);\n    checksum.writeUInt32BE(this.checksum >>> 0, 0);\n    return encoding ? checksum.toString(encoding) : checksum;\n  }\n\n  hex() {\n    return this.digest('hex').toUpperCase();\n  }\n\n  size() {\n    return this.rawSize;\n  }\n}\n\nmodule.exports = CRC32Stream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/crc32-stream/lib/crc32-stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/crc32-stream/lib/deflate-crc32-stream.js":
/*!***************************************************************!*\
  !*** ./node_modules/crc32-stream/lib/deflate-crc32-stream.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-crc32-stream\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-crc32-stream/blob/master/LICENSE-MIT\n */\n\n\n\nconst {DeflateRaw} = __webpack_require__(/*! zlib */ \"zlib\");\n\nconst crc32 = __webpack_require__(/*! crc-32 */ \"(ssr)/./node_modules/crc-32/crc32.js\");\n\nclass DeflateCRC32Stream extends DeflateRaw {\n  constructor(options) {\n    super(options);\n\n    this.checksum = Buffer.allocUnsafe(4);\n    this.checksum.writeInt32BE(0, 0);\n\n    this.rawSize = 0;\n    this.compressedSize = 0;\n  }\n\n  push(chunk, encoding) {\n    if (chunk) {\n      this.compressedSize += chunk.length;\n    }\n\n    return super.push(chunk, encoding);\n  }\n\n  _transform(chunk, encoding, callback) {\n    if (chunk) {\n      this.checksum = crc32.buf(chunk, this.checksum) >>> 0;\n      this.rawSize += chunk.length;\n    }\n\n    super._transform(chunk, encoding, callback)\n  }\n\n  digest(encoding) {\n    const checksum = Buffer.allocUnsafe(4);\n    checksum.writeUInt32BE(this.checksum >>> 0, 0);\n    return encoding ? checksum.toString(encoding) : checksum;\n  }\n\n  hex() {\n    return this.digest('hex').toUpperCase();\n  }\n\n  size(compressed = false) {\n    if (compressed) {\n      return this.compressedSize;\n    } else {\n      return this.rawSize;\n    }\n  }\n}\n\nmodule.exports = DeflateCRC32Stream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/crc32-stream/lib/deflate-crc32-stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/crc32-stream/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/crc32-stream/lib/index.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-crc32-stream\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-crc32-stream/blob/master/LICENSE-MIT\n */\n\n\n\nmodule.exports = {\n  CRC32Stream: __webpack_require__(/*! ./crc32-stream */ \"(ssr)/./node_modules/crc32-stream/lib/crc32-stream.js\"),\n  DeflateCRC32Stream: __webpack_require__(/*! ./deflate-crc32-stream */ \"(ssr)/./node_modules/crc32-stream/lib/deflate-crc32-stream.js\")\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY3JjMzItc3RyZWFtL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFYTs7QUFFYjtBQUNBLGVBQWUsbUJBQU8sQ0FBQyw2RUFBZ0I7QUFDdkMsc0JBQXNCLG1CQUFPLENBQUMsNkZBQXdCO0FBQ3REIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2xpZW50Ly4vbm9kZV9tb2R1bGVzL2NyYzMyLXN0cmVhbS9saWIvaW5kZXguanM/MzE5NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIG5vZGUtY3JjMzItc3RyZWFtXG4gKlxuICogQ29weXJpZ2h0IChjKSAyMDE0IENocmlzIFRhbGtpbmd0b24sIGNvbnRyaWJ1dG9ycy5cbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9hcmNoaXZlcmpzL25vZGUtY3JjMzItc3RyZWFtL2Jsb2IvbWFzdGVyL0xJQ0VOU0UtTUlUXG4gKi9cblxuJ3VzZSBzdHJpY3QnO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgQ1JDMzJTdHJlYW06IHJlcXVpcmUoJy4vY3JjMzItc3RyZWFtJyksXG4gIERlZmxhdGVDUkMzMlN0cmVhbTogcmVxdWlyZSgnLi9kZWZsYXRlLWNyYzMyLXN0cmVhbScpXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/crc32-stream/lib/index.js\n");

/***/ })

};
;
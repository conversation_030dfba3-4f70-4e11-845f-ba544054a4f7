"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUserTitle = void 0;
const operation_1 = require("../../../utils/operation");
const updateUserTitle = async (req, res) => {
    const id = req.params.id;
    const fields = {
        title: req.body.title,
        level: req.body.level,
    };
    await (0, operation_1.updateItem)({
        model: "userTitle",
        fieldName: "id",
        fields: fields,
        id: Number(id),
        res,
        req,
        successMessage: "User title has been updated",
    });
};
exports.updateUserTitle = updateUserTitle;
//# sourceMappingURL=update.js.map
export interface User {
  id: number
  firstName: string
  lastName: string
  email: string
  avatar?: string
}

export interface InvoiceFile {
  ticketId: unknown
  id: string
  date: string 
  fileName: string
  carrierId: number
  noOfPages: number
  assignedTo?: number
  assignedToUser?: User
  createdAt: string
  createdBy: number
  createdByUser?: User
  updatedAt?: string
  updatedBy?: number
  updatedByUser?: User
  deletedAt?: string
  deletedBy?: number
  deletedByUser?: User
}

export interface InvoiceFileFormData {
  date: string
  carrier: number
  fileName: string
  noOfPages: number
  assignedTo?: number
  createdBy?: number
}

export interface InvoiceFileStats {
  totalFiles: number
  pendingAssignment: number
  activeUsers: number
  totalPages: number
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface FilterParams {
  fileName?: string;         
  carrier?: string;          
  date?: string;            
  assignedTo?: number[]; 
  search?: string; 
  dateFrom?: string; 
  dateTo?: string;      
}



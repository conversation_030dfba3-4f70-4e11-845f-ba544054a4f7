model TrackSheets {
    id                 Int       @id @default(autoincrement())
    clientId           Int?      @map("client_id")
    client             Client?   @relation(fields: [clientId], references: [id], onDelete: Cascade)
    company            String?
    division           String?
    masterInvoice      String?   @map("master_invoice") @db.VarChar()
    invoice            String?   @db.VarChar()
    bol                String?   @db.VarChar()
    invoiceDate        DateTime? @map("invoice_date")
    receivedDate       DateTime? @map("received_date")
    shipmentDate       DateTime? @map("shipment_date")
    carrierId          Int?      @map("carrier_id")
    carrier            Carrier?  @relation(fields: [carrierId], references: [id], onDelete: Cascade)
    invoiceStatus      String?   @map("invoice_status")
    manualMatching     String?   @map("manual_matching")
    invoiceType        String?   @map("invoice_type")
    currency           String?
    qtyShipped         Int?      @map("qty_shipped")
    weightUnitName     String?   @map("weight_unit_name")
    quantityBilledText String?   @map("quantity_billed_text") @db.VarChar()
    freightClass       String?   @map("freight_class") @db.VarChar()
    invoiceTotal       Decimal?  @map("invoice_total") @db.Decimal(20, 6)
    savings            String?   @db.VarChar()
    financialNotes     String?   @map("financial_notes") @db.VarChar()
    ftpFileName        String?   @map("ftp_file_name") @db.VarChar()
    ftpPage            String?   @map("ftp_page") @db.VarChar()
    filePath           String?   @map("file_path") @db.VarChar()
    billToClient       Boolean?  @default(false) @map("bill_to_client")
    docAvailable       String?   @map("doc_available")
    notes              String?   @db.VarChar()
    mistake            String?   @db.VarChar()
    shipperAddress     String?   @map("shipper_address") @db.VarChar()
    consigneeAddress   String?   @map("consignee_address") @db.VarChar()
    billToAddress      String?   @map("bill_to_address") @db.VarChar()
    finalInvoice       Boolean?  @default(false) @map("final_invoice")

    // New freight and address type fields
    freightTerm          FreightTerm? @default(LEGACY) @map("freight_term")
    shipperAddressType   AddressType? @default(LEGACY) @map("shipper_address_type")
    consigneeAddressType AddressType? @default(LEGACY) @map("consignee_address_type")
    billToAddressType    AddressType? @default(LEGACY) @map("bill_to_address_type")

    manifestDetails ManifestDetailsSchema? @relation("TrackSheetToManifest")

    trackSheetImportId String?           @map("track_sheet_import_id")
    trackSheetImport   TrackSheetImport? @relation(fields: [trackSheetImportId], references: [id], onDelete: Cascade)

    invoiceFileId String?      @map("invoice_file_id")
    invoiceFile   InvoiceFile? @relation(fields: [invoiceFileId], references: [id], onDelete: Cascade)

    TrackSheetCustomFieldMapping TrackSheetCustomFieldMapping[]
    systemGeneratedWarnings      SystemGeneratedWarning[]

    createdAt DateTime @default(now()) @map("created_at")
    updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
    enteredBy String?  @map("entered_by") @db.VarChar()

    @@index([invoice])
    @@map("track_sheets")
}

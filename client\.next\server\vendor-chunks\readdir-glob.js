/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/readdir-glob";
exports.ids = ["vendor-chunks/readdir-glob"];
exports.modules = {

/***/ "(ssr)/./node_modules/readdir-glob/index.js":
/*!********************************************!*\
  !*** ./node_modules/readdir-glob/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = readdirGlob;\n\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst { EventEmitter } = __webpack_require__(/*! events */ \"events\");\nconst { Minimatch } = __webpack_require__(/*! minimatch */ \"(ssr)/./node_modules/readdir-glob/node_modules/minimatch/minimatch.js\");\nconst { resolve } = __webpack_require__(/*! path */ \"path\");\n\nfunction readdir(dir, strict) {\n  return new Promise((resolve, reject) => {\n    fs.readdir(dir, {withFileTypes: true} ,(err, files) => {\n      if(err) {\n        switch (err.code) {\n          case 'ENOTDIR':      // Not a directory\n            if(strict) {\n              reject(err);\n            } else {\n              resolve([]);\n            }\n            break;\n          case 'ENOTSUP':      // Operation not supported\n          case 'ENOENT':       // No such file or directory\n          case 'ENAMETOOLONG': // Filename too long\n          case 'UNKNOWN':\n            resolve([]);\n            break;\n          case 'ELOOP':        // Too many levels of symbolic links\n          default:\n            reject(err);\n            break;\n        }\n      } else {\n        resolve(files);\n      }\n    });\n  });\n}\nfunction stat(file, followSymlinks) {\n  return new Promise((resolve, reject) => {\n    const statFunc = followSymlinks ? fs.stat : fs.lstat;\n    statFunc(file, (err, stats) => {\n      if(err) {\n        switch (err.code) {\n          case 'ENOENT':\n            if(followSymlinks) {\n              // Fallback to lstat to handle broken links as files\n              resolve(stat(file, false)); \n            } else {\n              resolve(null);\n            }\n            break;\n          default:\n            resolve(null);\n            break;\n        }\n      } else {\n        resolve(stats);\n      }\n    });\n  });\n}\n\nasync function* exploreWalkAsync(dir, path, followSymlinks, useStat, shouldSkip, strict) {\n  let files = await readdir(path + dir, strict);\n  for(const file of files) {\n    let name = file.name;\n    if(name === undefined) {\n      // undefined file.name means the `withFileTypes` options is not supported by node\n      // we have to call the stat function to know if file is directory or not.\n      name = file;\n      useStat = true;\n    }\n    const filename = dir + '/' + name;\n    const relative = filename.slice(1); // Remove the leading /\n    const absolute = path + '/' + relative;\n    let stats = null;\n    if(useStat || followSymlinks) {\n      stats = await stat(absolute, followSymlinks);\n    }\n    if(!stats && file.name !== undefined) {\n      stats = file;\n    }\n    if(stats === null) {\n      stats = { isDirectory: () => false };\n    }\n\n    if(stats.isDirectory()) {\n      if(!shouldSkip(relative)) {\n        yield {relative, absolute, stats};\n        yield* exploreWalkAsync(filename, path, followSymlinks, useStat, shouldSkip, false);\n      }\n    } else {\n      yield {relative, absolute, stats};\n    }\n  }\n}\nasync function* explore(path, followSymlinks, useStat, shouldSkip) {\n  yield* exploreWalkAsync('', path, followSymlinks, useStat, shouldSkip, true);\n}\n\n\nfunction readOptions(options) {\n  return {\n    pattern: options.pattern,\n    dot: !!options.dot,\n    noglobstar: !!options.noglobstar,\n    matchBase: !!options.matchBase,\n    nocase: !!options.nocase,\n    ignore: options.ignore,\n    skip: options.skip,\n\n    follow: !!options.follow,\n    stat: !!options.stat,\n    nodir: !!options.nodir,\n    mark: !!options.mark,\n    silent: !!options.silent,\n    absolute: !!options.absolute\n  };\n}\n\nclass ReaddirGlob extends EventEmitter {\n  constructor(cwd, options, cb) {\n    super();\n    if(typeof options === 'function') {\n      cb = options;\n      options = null;\n    }\n\n    this.options = readOptions(options || {});\n  \n    this.matchers = [];\n    if(this.options.pattern) {\n      const matchers = Array.isArray(this.options.pattern) ? this.options.pattern : [this.options.pattern];\n      this.matchers = matchers.map( m =>\n        new Minimatch(m, {\n          dot: this.options.dot,\n          noglobstar:this.options.noglobstar,\n          matchBase:this.options.matchBase,\n          nocase:this.options.nocase\n        })\n      );\n    }\n  \n    this.ignoreMatchers = [];\n    if(this.options.ignore) {\n      const ignorePatterns = Array.isArray(this.options.ignore) ? this.options.ignore : [this.options.ignore];\n      this.ignoreMatchers = ignorePatterns.map( ignore =>\n        new Minimatch(ignore, {dot: true})\n      );\n    }\n  \n    this.skipMatchers = [];\n    if(this.options.skip) {\n      const skipPatterns = Array.isArray(this.options.skip) ? this.options.skip : [this.options.skip];\n      this.skipMatchers = skipPatterns.map( skip =>\n        new Minimatch(skip, {dot: true})\n      );\n    }\n\n    this.iterator = explore(resolve(cwd || '.'), this.options.follow, this.options.stat, this._shouldSkipDirectory.bind(this));\n    this.paused = false;\n    this.inactive = false;\n    this.aborted = false;\n  \n    if(cb) {\n      this._matches = []; \n      this.on('match', match => this._matches.push(this.options.absolute ? match.absolute : match.relative));\n      this.on('error', err => cb(err));\n      this.on('end', () => cb(null, this._matches));\n    }\n\n    setTimeout( () => this._next(), 0);\n  }\n\n  _shouldSkipDirectory(relative) {\n    //console.log(relative, this.skipMatchers.some(m => m.match(relative)));\n    return this.skipMatchers.some(m => m.match(relative));\n  }\n\n  _fileMatches(relative, isDirectory) {\n    const file = relative + (isDirectory ? '/' : '');\n    return (this.matchers.length === 0 || this.matchers.some(m => m.match(file)))\n      && !this.ignoreMatchers.some(m => m.match(file))\n      && (!this.options.nodir || !isDirectory);\n  }\n\n  _next() {\n    if(!this.paused && !this.aborted) {\n      this.iterator.next()\n      .then((obj)=> {\n        if(!obj.done) {\n          const isDirectory = obj.value.stats.isDirectory();\n          if(this._fileMatches(obj.value.relative, isDirectory )) {\n            let relative = obj.value.relative;\n            let absolute = obj.value.absolute;\n            if(this.options.mark && isDirectory) {\n              relative += '/';\n              absolute += '/';\n            }\n            if(this.options.stat) {\n              this.emit('match', {relative, absolute, stat:obj.value.stats});\n            } else {\n              this.emit('match', {relative, absolute});\n            }\n          }\n          this._next(this.iterator);\n        } else {\n          this.emit('end');\n        }\n      })\n      .catch((err) => {\n        this.abort();\n        this.emit('error', err);\n        if(!err.code && !this.options.silent) {\n          console.error(err);\n        }\n      });\n    } else {\n      this.inactive = true;\n    }\n  }\n\n  abort() {\n    this.aborted = true;\n  }\n\n  pause() {\n    this.paused = true;\n  }\n\n  resume() {\n    this.paused = false;\n    if(this.inactive) {\n      this.inactive = false;\n      this._next();\n    }\n  }\n}\n\n\nfunction readdirGlob(pattern, options, cb) {\n  return new ReaddirGlob(pattern, options, cb);\n}\nreaddirGlob.ReaddirGlob = ReaddirGlob;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readdir-glob/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readdir-glob/node_modules/brace-expansion/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/readdir-glob/node_modules/brace-expansion/index.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var balanced = __webpack_require__(/*! balanced-match */ \"(ssr)/./node_modules/balanced-match/index.js\");\n\nmodule.exports = expandTop;\n\nvar escSlash = '\\0SLASH'+Math.random()+'\\0';\nvar escOpen = '\\0OPEN'+Math.random()+'\\0';\nvar escClose = '\\0CLOSE'+Math.random()+'\\0';\nvar escComma = '\\0COMMA'+Math.random()+'\\0';\nvar escPeriod = '\\0PERIOD'+Math.random()+'\\0';\n\nfunction numeric(str) {\n  return parseInt(str, 10) == str\n    ? parseInt(str, 10)\n    : str.charCodeAt(0);\n}\n\nfunction escapeBraces(str) {\n  return str.split('\\\\\\\\').join(escSlash)\n            .split('\\\\{').join(escOpen)\n            .split('\\\\}').join(escClose)\n            .split('\\\\,').join(escComma)\n            .split('\\\\.').join(escPeriod);\n}\n\nfunction unescapeBraces(str) {\n  return str.split(escSlash).join('\\\\')\n            .split(escOpen).join('{')\n            .split(escClose).join('}')\n            .split(escComma).join(',')\n            .split(escPeriod).join('.');\n}\n\n\n// Basically just str.split(\",\"), but handling cases\n// where we have nested braced sections, which should be\n// treated as individual members, like {a,{b,c},d}\nfunction parseCommaParts(str) {\n  if (!str)\n    return [''];\n\n  var parts = [];\n  var m = balanced('{', '}', str);\n\n  if (!m)\n    return str.split(',');\n\n  var pre = m.pre;\n  var body = m.body;\n  var post = m.post;\n  var p = pre.split(',');\n\n  p[p.length-1] += '{' + body + '}';\n  var postParts = parseCommaParts(post);\n  if (post.length) {\n    p[p.length-1] += postParts.shift();\n    p.push.apply(p, postParts);\n  }\n\n  parts.push.apply(parts, p);\n\n  return parts;\n}\n\nfunction expandTop(str) {\n  if (!str)\n    return [];\n\n  // I don't know why Bash 4.3 does this, but it does.\n  // Anything starting with {} will have the first two bytes preserved\n  // but *only* at the top level, so {},a}b will not expand to anything,\n  // but a{},b}c will be expanded to [a}c,abc].\n  // One could argue that this is a bug in Bash, but since the goal of\n  // this module is to match Bash's rules, we escape a leading {}\n  if (str.substr(0, 2) === '{}') {\n    str = '\\\\{\\\\}' + str.substr(2);\n  }\n\n  return expand(escapeBraces(str), true).map(unescapeBraces);\n}\n\nfunction embrace(str) {\n  return '{' + str + '}';\n}\nfunction isPadded(el) {\n  return /^-?0\\d/.test(el);\n}\n\nfunction lte(i, y) {\n  return i <= y;\n}\nfunction gte(i, y) {\n  return i >= y;\n}\n\nfunction expand(str, isTop) {\n  var expansions = [];\n\n  var m = balanced('{', '}', str);\n  if (!m) return [str];\n\n  // no need to expand pre, since it is guaranteed to be free of brace-sets\n  var pre = m.pre;\n  var post = m.post.length\n    ? expand(m.post, false)\n    : [''];\n\n  if (/\\$$/.test(m.pre)) {    \n    for (var k = 0; k < post.length; k++) {\n      var expansion = pre+ '{' + m.body + '}' + post[k];\n      expansions.push(expansion);\n    }\n  } else {\n    var isNumericSequence = /^-?\\d+\\.\\.-?\\d+(?:\\.\\.-?\\d+)?$/.test(m.body);\n    var isAlphaSequence = /^[a-zA-Z]\\.\\.[a-zA-Z](?:\\.\\.-?\\d+)?$/.test(m.body);\n    var isSequence = isNumericSequence || isAlphaSequence;\n    var isOptions = m.body.indexOf(',') >= 0;\n    if (!isSequence && !isOptions) {\n      // {a},b}\n      if (m.post.match(/,.*\\}/)) {\n        str = m.pre + '{' + m.body + escClose + m.post;\n        return expand(str);\n      }\n      return [str];\n    }\n\n    var n;\n    if (isSequence) {\n      n = m.body.split(/\\.\\./);\n    } else {\n      n = parseCommaParts(m.body);\n      if (n.length === 1) {\n        // x{{a,b}}y ==> x{a}y x{b}y\n        n = expand(n[0], false).map(embrace);\n        if (n.length === 1) {\n          return post.map(function(p) {\n            return m.pre + n[0] + p;\n          });\n        }\n      }\n    }\n\n    // at this point, n is the parts, and we know it's not a comma set\n    // with a single entry.\n    var N;\n\n    if (isSequence) {\n      var x = numeric(n[0]);\n      var y = numeric(n[1]);\n      var width = Math.max(n[0].length, n[1].length)\n      var incr = n.length == 3\n        ? Math.abs(numeric(n[2]))\n        : 1;\n      var test = lte;\n      var reverse = y < x;\n      if (reverse) {\n        incr *= -1;\n        test = gte;\n      }\n      var pad = n.some(isPadded);\n\n      N = [];\n\n      for (var i = x; test(i, y); i += incr) {\n        var c;\n        if (isAlphaSequence) {\n          c = String.fromCharCode(i);\n          if (c === '\\\\')\n            c = '';\n        } else {\n          c = String(i);\n          if (pad) {\n            var need = width - c.length;\n            if (need > 0) {\n              var z = new Array(need + 1).join('0');\n              if (i < 0)\n                c = '-' + z + c.slice(1);\n              else\n                c = z + c;\n            }\n          }\n        }\n        N.push(c);\n      }\n    } else {\n      N = [];\n\n      for (var j = 0; j < n.length; j++) {\n        N.push.apply(N, expand(n[j], false));\n      }\n    }\n\n    for (var j = 0; j < N.length; j++) {\n      for (var k = 0; k < post.length; k++) {\n        var expansion = pre + N[j] + post[k];\n        if (!isTop || isSequence || expansion)\n          expansions.push(expansion);\n      }\n    }\n  }\n\n  return expansions;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readdir-glob/node_modules/brace-expansion/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readdir-glob/node_modules/minimatch/lib/path.js":
/*!**********************************************************************!*\
  !*** ./node_modules/readdir-glob/node_modules/minimatch/lib/path.js ***!
  \**********************************************************************/
/***/ ((module) => {

eval("const isWindows = typeof process === 'object' &&\n  process &&\n  process.platform === 'win32'\nmodule.exports = isWindows ? { sep: '\\\\' } : { sep: '/' }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhZGRpci1nbG9iL25vZGVfbW9kdWxlcy9taW5pbWF0Y2gvbGliL3BhdGguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLFlBQVksSUFBSSIsInNvdXJjZXMiOlsid2VicGFjazovL2NsaWVudC8uL25vZGVfbW9kdWxlcy9yZWFkZGlyLWdsb2Ivbm9kZV9tb2R1bGVzL21pbmltYXRjaC9saWIvcGF0aC5qcz8zYmYxIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzV2luZG93cyA9IHR5cGVvZiBwcm9jZXNzID09PSAnb2JqZWN0JyAmJlxuICBwcm9jZXNzICYmXG4gIHByb2Nlc3MucGxhdGZvcm0gPT09ICd3aW4zMidcbm1vZHVsZS5leHBvcnRzID0gaXNXaW5kb3dzID8geyBzZXA6ICdcXFxcJyB9IDogeyBzZXA6ICcvJyB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readdir-glob/node_modules/minimatch/lib/path.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readdir-glob/node_modules/minimatch/minimatch.js":
/*!***********************************************************************!*\
  !*** ./node_modules/readdir-glob/node_modules/minimatch/minimatch.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const minimatch = module.exports = (p, pattern, options = {}) => {\n  assertValidPattern(pattern)\n\n  // shortcut: comments match nothing.\n  if (!options.nocomment && pattern.charAt(0) === '#') {\n    return false\n  }\n\n  return new Minimatch(pattern, options).match(p)\n}\n\nmodule.exports = minimatch\n\nconst path = __webpack_require__(/*! ./lib/path.js */ \"(ssr)/./node_modules/readdir-glob/node_modules/minimatch/lib/path.js\")\nminimatch.sep = path.sep\n\nconst GLOBSTAR = Symbol('globstar **')\nminimatch.GLOBSTAR = GLOBSTAR\nconst expand = __webpack_require__(/*! brace-expansion */ \"(ssr)/./node_modules/readdir-glob/node_modules/brace-expansion/index.js\")\n\nconst plTypes = {\n  '!': { open: '(?:(?!(?:', close: '))[^/]*?)'},\n  '?': { open: '(?:', close: ')?' },\n  '+': { open: '(?:', close: ')+' },\n  '*': { open: '(?:', close: ')*' },\n  '@': { open: '(?:', close: ')' }\n}\n\n// any single thing other than /\n// don't need to escape / when using new RegExp()\nconst qmark = '[^/]'\n\n// * => any number of characters\nconst star = qmark + '*?'\n\n// ** when dots are allowed.  Anything goes, except .. and .\n// not (^ or / followed by one or two dots followed by $ or /),\n// followed by anything, any number of times.\nconst twoStarDot = '(?:(?!(?:\\\\\\/|^)(?:\\\\.{1,2})($|\\\\\\/)).)*?'\n\n// not a ^ or / followed by a dot,\n// followed by anything, any number of times.\nconst twoStarNoDot = '(?:(?!(?:\\\\\\/|^)\\\\.).)*?'\n\n// \"abc\" -> { a:true, b:true, c:true }\nconst charSet = s => s.split('').reduce((set, c) => {\n  set[c] = true\n  return set\n}, {})\n\n// characters that need to be escaped in RegExp.\nconst reSpecials = charSet('().*{}+?[]^$\\\\!')\n\n// characters that indicate we have to add the pattern start\nconst addPatternStartSet = charSet('[.(')\n\n// normalizes slashes.\nconst slashSplit = /\\/+/\n\nminimatch.filter = (pattern, options = {}) =>\n  (p, i, list) => minimatch(p, pattern, options)\n\nconst ext = (a, b = {}) => {\n  const t = {}\n  Object.keys(a).forEach(k => t[k] = a[k])\n  Object.keys(b).forEach(k => t[k] = b[k])\n  return t\n}\n\nminimatch.defaults = def => {\n  if (!def || typeof def !== 'object' || !Object.keys(def).length) {\n    return minimatch\n  }\n\n  const orig = minimatch\n\n  const m = (p, pattern, options) => orig(p, pattern, ext(def, options))\n  m.Minimatch = class Minimatch extends orig.Minimatch {\n    constructor (pattern, options) {\n      super(pattern, ext(def, options))\n    }\n  }\n  m.Minimatch.defaults = options => orig.defaults(ext(def, options)).Minimatch\n  m.filter = (pattern, options) => orig.filter(pattern, ext(def, options))\n  m.defaults = options => orig.defaults(ext(def, options))\n  m.makeRe = (pattern, options) => orig.makeRe(pattern, ext(def, options))\n  m.braceExpand = (pattern, options) => orig.braceExpand(pattern, ext(def, options))\n  m.match = (list, pattern, options) => orig.match(list, pattern, ext(def, options))\n\n  return m\n}\n\n\n\n\n\n// Brace expansion:\n// a{b,c}d -> abd acd\n// a{b,}c -> abc ac\n// a{0..3}d -> a0d a1d a2d a3d\n// a{b,c{d,e}f}g -> abg acdfg acefg\n// a{b,c}d{e,f}g -> abdeg acdeg abdeg abdfg\n//\n// Invalid sets are not expanded.\n// a{2..}b -> a{2..}b\n// a{b}c -> a{b}c\nminimatch.braceExpand = (pattern, options) => braceExpand(pattern, options)\n\nconst braceExpand = (pattern, options = {}) => {\n  assertValidPattern(pattern)\n\n  // Thanks to Yeting Li <https://github.com/yetingli> for\n  // improving this regexp to avoid a ReDOS vulnerability.\n  if (options.nobrace || !/\\{(?:(?!\\{).)*\\}/.test(pattern)) {\n    // shortcut. no need to expand.\n    return [pattern]\n  }\n\n  return expand(pattern)\n}\n\nconst MAX_PATTERN_LENGTH = 1024 * 64\nconst assertValidPattern = pattern => {\n  if (typeof pattern !== 'string') {\n    throw new TypeError('invalid pattern')\n  }\n\n  if (pattern.length > MAX_PATTERN_LENGTH) {\n    throw new TypeError('pattern is too long')\n  }\n}\n\n// parse a component of the expanded set.\n// At this point, no pattern may contain \"/\" in it\n// so we're going to return a 2d array, where each entry is the full\n// pattern, split on '/', and then turned into a regular expression.\n// A regexp is made at the end which joins each array with an\n// escaped /, and another full one which joins each regexp with |.\n//\n// Following the lead of Bash 4.1, note that \"**\" only has special meaning\n// when it is the *only* thing in a path portion.  Otherwise, any series\n// of * is equivalent to a single *.  Globstar behavior is enabled by\n// default, and can be disabled by setting options.noglobstar.\nconst SUBPARSE = Symbol('subparse')\n\nminimatch.makeRe = (pattern, options) =>\n  new Minimatch(pattern, options || {}).makeRe()\n\nminimatch.match = (list, pattern, options = {}) => {\n  const mm = new Minimatch(pattern, options)\n  list = list.filter(f => mm.match(f))\n  if (mm.options.nonull && !list.length) {\n    list.push(pattern)\n  }\n  return list\n}\n\n// replace stuff like \\* with *\nconst globUnescape = s => s.replace(/\\\\(.)/g, '$1')\nconst charUnescape = s => s.replace(/\\\\([^-\\]])/g, '$1')\nconst regExpEscape = s => s.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&')\nconst braExpEscape = s => s.replace(/[[\\]\\\\]/g, '\\\\$&')\n\nclass Minimatch {\n  constructor (pattern, options) {\n    assertValidPattern(pattern)\n\n    if (!options) options = {}\n\n    this.options = options\n    this.set = []\n    this.pattern = pattern\n    this.windowsPathsNoEscape = !!options.windowsPathsNoEscape ||\n      options.allowWindowsEscape === false\n    if (this.windowsPathsNoEscape) {\n      this.pattern = this.pattern.replace(/\\\\/g, '/')\n    }\n    this.regexp = null\n    this.negate = false\n    this.comment = false\n    this.empty = false\n    this.partial = !!options.partial\n\n    // make the set of regexps etc.\n    this.make()\n  }\n\n  debug () {}\n\n  make () {\n    const pattern = this.pattern\n    const options = this.options\n\n    // empty patterns and comments match nothing.\n    if (!options.nocomment && pattern.charAt(0) === '#') {\n      this.comment = true\n      return\n    }\n    if (!pattern) {\n      this.empty = true\n      return\n    }\n\n    // step 1: figure out negation, etc.\n    this.parseNegate()\n\n    // step 2: expand braces\n    let set = this.globSet = this.braceExpand()\n\n    if (options.debug) this.debug = (...args) => console.error(...args)\n\n    this.debug(this.pattern, set)\n\n    // step 3: now we have a set, so turn each one into a series of path-portion\n    // matching patterns.\n    // These will be regexps, except in the case of \"**\", which is\n    // set to the GLOBSTAR object for globstar behavior,\n    // and will not contain any / characters\n    set = this.globParts = set.map(s => s.split(slashSplit))\n\n    this.debug(this.pattern, set)\n\n    // glob --> regexps\n    set = set.map((s, si, set) => s.map(this.parse, this))\n\n    this.debug(this.pattern, set)\n\n    // filter out everything that didn't compile properly.\n    set = set.filter(s => s.indexOf(false) === -1)\n\n    this.debug(this.pattern, set)\n\n    this.set = set\n  }\n\n  parseNegate () {\n    if (this.options.nonegate) return\n\n    const pattern = this.pattern\n    let negate = false\n    let negateOffset = 0\n\n    for (let i = 0; i < pattern.length && pattern.charAt(i) === '!'; i++) {\n      negate = !negate\n      negateOffset++\n    }\n\n    if (negateOffset) this.pattern = pattern.slice(negateOffset)\n    this.negate = negate\n  }\n\n  // set partial to true to test if, for example,\n  // \"/a/b\" matches the start of \"/*/b/*/d\"\n  // Partial means, if you run out of file before you run\n  // out of pattern, then that's fine, as long as all\n  // the parts match.\n  matchOne (file, pattern, partial) {\n    var options = this.options\n\n    this.debug('matchOne',\n      { 'this': this, file: file, pattern: pattern })\n\n    this.debug('matchOne', file.length, pattern.length)\n\n    for (var fi = 0,\n        pi = 0,\n        fl = file.length,\n        pl = pattern.length\n        ; (fi < fl) && (pi < pl)\n        ; fi++, pi++) {\n      this.debug('matchOne loop')\n      var p = pattern[pi]\n      var f = file[fi]\n\n      this.debug(pattern, p, f)\n\n      // should be impossible.\n      // some invalid regexp stuff in the set.\n      /* istanbul ignore if */\n      if (p === false) return false\n\n      if (p === GLOBSTAR) {\n        this.debug('GLOBSTAR', [pattern, p, f])\n\n        // \"**\"\n        // a/**/b/**/c would match the following:\n        // a/b/x/y/z/c\n        // a/x/y/z/b/c\n        // a/b/x/b/x/c\n        // a/b/c\n        // To do this, take the rest of the pattern after\n        // the **, and see if it would match the file remainder.\n        // If so, return success.\n        // If not, the ** \"swallows\" a segment, and try again.\n        // This is recursively awful.\n        //\n        // a/**/b/**/c matching a/b/x/y/z/c\n        // - a matches a\n        // - doublestar\n        //   - matchOne(b/x/y/z/c, b/**/c)\n        //     - b matches b\n        //     - doublestar\n        //       - matchOne(x/y/z/c, c) -> no\n        //       - matchOne(y/z/c, c) -> no\n        //       - matchOne(z/c, c) -> no\n        //       - matchOne(c, c) yes, hit\n        var fr = fi\n        var pr = pi + 1\n        if (pr === pl) {\n          this.debug('** at the end')\n          // a ** at the end will just swallow the rest.\n          // We have found a match.\n          // however, it will not swallow /.x, unless\n          // options.dot is set.\n          // . and .. are *never* matched by **, for explosively\n          // exponential reasons.\n          for (; fi < fl; fi++) {\n            if (file[fi] === '.' || file[fi] === '..' ||\n              (!options.dot && file[fi].charAt(0) === '.')) return false\n          }\n          return true\n        }\n\n        // ok, let's see if we can swallow whatever we can.\n        while (fr < fl) {\n          var swallowee = file[fr]\n\n          this.debug('\\nglobstar while', file, fr, pattern, pr, swallowee)\n\n          // XXX remove this slice.  Just pass the start index.\n          if (this.matchOne(file.slice(fr), pattern.slice(pr), partial)) {\n            this.debug('globstar found match!', fr, fl, swallowee)\n            // found a match.\n            return true\n          } else {\n            // can't swallow \".\" or \"..\" ever.\n            // can only swallow \".foo\" when explicitly asked.\n            if (swallowee === '.' || swallowee === '..' ||\n              (!options.dot && swallowee.charAt(0) === '.')) {\n              this.debug('dot detected!', file, fr, pattern, pr)\n              break\n            }\n\n            // ** swallows a segment, and continue.\n            this.debug('globstar swallow a segment, and continue')\n            fr++\n          }\n        }\n\n        // no match was found.\n        // However, in partial mode, we can't say this is necessarily over.\n        // If there's more *pattern* left, then\n        /* istanbul ignore if */\n        if (partial) {\n          // ran out of file\n          this.debug('\\n>>> no match, partial?', file, fr, pattern, pr)\n          if (fr === fl) return true\n        }\n        return false\n      }\n\n      // something other than **\n      // non-magic patterns just have to match exactly\n      // patterns with magic have been turned into regexps.\n      var hit\n      if (typeof p === 'string') {\n        hit = f === p\n        this.debug('string match', p, f, hit)\n      } else {\n        hit = f.match(p)\n        this.debug('pattern match', p, f, hit)\n      }\n\n      if (!hit) return false\n    }\n\n    // Note: ending in / means that we'll get a final \"\"\n    // at the end of the pattern.  This can only match a\n    // corresponding \"\" at the end of the file.\n    // If the file ends in /, then it can only match a\n    // a pattern that ends in /, unless the pattern just\n    // doesn't have any more for it. But, a/b/ should *not*\n    // match \"a/b/*\", even though \"\" matches against the\n    // [^/]*? pattern, except in partial mode, where it might\n    // simply not be reached yet.\n    // However, a/b/ should still satisfy a/*\n\n    // now either we fell off the end of the pattern, or we're done.\n    if (fi === fl && pi === pl) {\n      // ran out of pattern and filename at the same time.\n      // an exact hit!\n      return true\n    } else if (fi === fl) {\n      // ran out of file, but still had pattern left.\n      // this is ok if we're doing the match as part of\n      // a glob fs traversal.\n      return partial\n    } else /* istanbul ignore else */ if (pi === pl) {\n      // ran out of pattern, still have file left.\n      // this is only acceptable if we're on the very last\n      // empty segment of a file with a trailing slash.\n      // a/* should match a/b/\n      return (fi === fl - 1) && (file[fi] === '')\n    }\n\n    // should be unreachable.\n    /* istanbul ignore next */\n    throw new Error('wtf?')\n  }\n\n  braceExpand () {\n    return braceExpand(this.pattern, this.options)\n  }\n\n  parse (pattern, isSub) {\n    assertValidPattern(pattern)\n\n    const options = this.options\n\n    // shortcuts\n    if (pattern === '**') {\n      if (!options.noglobstar)\n        return GLOBSTAR\n      else\n        pattern = '*'\n    }\n    if (pattern === '') return ''\n\n    let re = ''\n    let hasMagic = false\n    let escaping = false\n    // ? => one single character\n    const patternListStack = []\n    const negativeLists = []\n    let stateChar\n    let inClass = false\n    let reClassStart = -1\n    let classStart = -1\n    let cs\n    let pl\n    let sp\n    // . and .. never match anything that doesn't start with .,\n    // even when options.dot is set.  However, if the pattern\n    // starts with ., then traversal patterns can match.\n    let dotTravAllowed = pattern.charAt(0) === '.'\n    let dotFileAllowed = options.dot || dotTravAllowed\n    const patternStart = () =>\n      dotTravAllowed\n        ? ''\n        : dotFileAllowed\n        ? '(?!(?:^|\\\\/)\\\\.{1,2}(?:$|\\\\/))'\n        : '(?!\\\\.)'\n    const subPatternStart = (p) =>\n      p.charAt(0) === '.'\n        ? ''\n        : options.dot\n        ? '(?!(?:^|\\\\/)\\\\.{1,2}(?:$|\\\\/))'\n        : '(?!\\\\.)'\n\n\n    const clearStateChar = () => {\n      if (stateChar) {\n        // we had some state-tracking character\n        // that wasn't consumed by this pass.\n        switch (stateChar) {\n          case '*':\n            re += star\n            hasMagic = true\n          break\n          case '?':\n            re += qmark\n            hasMagic = true\n          break\n          default:\n            re += '\\\\' + stateChar\n          break\n        }\n        this.debug('clearStateChar %j %j', stateChar, re)\n        stateChar = false\n      }\n    }\n\n    for (let i = 0, c; (i < pattern.length) && (c = pattern.charAt(i)); i++) {\n      this.debug('%s\\t%s %s %j', pattern, i, re, c)\n\n      // skip over any that are escaped.\n      if (escaping) {\n        /* istanbul ignore next - completely not allowed, even escaped. */\n        if (c === '/') {\n          return false\n        }\n\n        if (reSpecials[c]) {\n          re += '\\\\'\n        }\n        re += c\n        escaping = false\n        continue\n      }\n\n      switch (c) {\n        /* istanbul ignore next */\n        case '/': {\n          // Should already be path-split by now.\n          return false\n        }\n\n        case '\\\\':\n          if (inClass && pattern.charAt(i + 1) === '-') {\n            re += c\n            continue\n          }\n\n          clearStateChar()\n          escaping = true\n        continue\n\n        // the various stateChar values\n        // for the \"extglob\" stuff.\n        case '?':\n        case '*':\n        case '+':\n        case '@':\n        case '!':\n          this.debug('%s\\t%s %s %j <-- stateChar', pattern, i, re, c)\n\n          // all of those are literals inside a class, except that\n          // the glob [!a] means [^a] in regexp\n          if (inClass) {\n            this.debug('  in class')\n            if (c === '!' && i === classStart + 1) c = '^'\n            re += c\n            continue\n          }\n\n          // if we already have a stateChar, then it means\n          // that there was something like ** or +? in there.\n          // Handle the stateChar, then proceed with this one.\n          this.debug('call clearStateChar %j', stateChar)\n          clearStateChar()\n          stateChar = c\n          // if extglob is disabled, then +(asdf|foo) isn't a thing.\n          // just clear the statechar *now*, rather than even diving into\n          // the patternList stuff.\n          if (options.noext) clearStateChar()\n        continue\n\n        case '(': {\n          if (inClass) {\n            re += '('\n            continue\n          }\n\n          if (!stateChar) {\n            re += '\\\\('\n            continue\n          }\n\n          const plEntry = {\n            type: stateChar,\n            start: i - 1,\n            reStart: re.length,\n            open: plTypes[stateChar].open,\n            close: plTypes[stateChar].close,\n          }\n          this.debug(this.pattern, '\\t', plEntry)\n          patternListStack.push(plEntry)\n          // negation is (?:(?!(?:js)(?:<rest>))[^/]*)\n          re += plEntry.open\n          // next entry starts with a dot maybe?\n          if (plEntry.start === 0 && plEntry.type !== '!') {\n            dotTravAllowed = true\n            re += subPatternStart(pattern.slice(i + 1))\n          }\n          this.debug('plType %j %j', stateChar, re)\n          stateChar = false\n          continue\n        }\n\n        case ')': {\n          const plEntry = patternListStack[patternListStack.length - 1]\n          if (inClass || !plEntry) {\n            re += '\\\\)'\n            continue\n          }\n          patternListStack.pop()\n\n          // closing an extglob\n          clearStateChar()\n          hasMagic = true\n          pl = plEntry\n          // negation is (?:(?!js)[^/]*)\n          // The others are (?:<pattern>)<type>\n          re += pl.close\n          if (pl.type === '!') {\n            negativeLists.push(Object.assign(pl, { reEnd: re.length }))\n          }\n          continue\n        }\n\n        case '|': {\n          const plEntry = patternListStack[patternListStack.length - 1]\n          if (inClass || !plEntry) {\n            re += '\\\\|'\n            continue\n          }\n\n          clearStateChar()\n          re += '|'\n          // next subpattern can start with a dot?\n          if (plEntry.start === 0 && plEntry.type !== '!') {\n            dotTravAllowed = true\n            re += subPatternStart(pattern.slice(i + 1))\n          }\n          continue\n        }\n\n        // these are mostly the same in regexp and glob\n        case '[':\n          // swallow any state-tracking char before the [\n          clearStateChar()\n\n          if (inClass) {\n            re += '\\\\' + c\n            continue\n          }\n\n          inClass = true\n          classStart = i\n          reClassStart = re.length\n          re += c\n        continue\n\n        case ']':\n          //  a right bracket shall lose its special\n          //  meaning and represent itself in\n          //  a bracket expression if it occurs\n          //  first in the list.  -- POSIX.2 2.8.3.2\n          if (i === classStart + 1 || !inClass) {\n            re += '\\\\' + c\n            continue\n          }\n\n          // split where the last [ was, make sure we don't have\n          // an invalid re. if so, re-walk the contents of the\n          // would-be class to re-translate any characters that\n          // were passed through as-is\n          // TODO: It would probably be faster to determine this\n          // without a try/catch and a new RegExp, but it's tricky\n          // to do safely.  For now, this is safe and works.\n          cs = pattern.substring(classStart + 1, i)\n          try {\n            RegExp('[' + braExpEscape(charUnescape(cs)) + ']')\n            // looks good, finish up the class.\n            re += c\n          } catch (er) {\n            // out of order ranges in JS are errors, but in glob syntax,\n            // they're just a range that matches nothing.\n            re = re.substring(0, reClassStart) + '(?:$.)' // match nothing ever\n          }\n          hasMagic = true\n          inClass = false\n        continue\n\n        default:\n          // swallow any state char that wasn't consumed\n          clearStateChar()\n\n          if (reSpecials[c] && !(c === '^' && inClass)) {\n            re += '\\\\'\n          }\n\n          re += c\n          break\n\n      } // switch\n    } // for\n\n    // handle the case where we left a class open.\n    // \"[abc\" is valid, equivalent to \"\\[abc\"\n    if (inClass) {\n      // split where the last [ was, and escape it\n      // this is a huge pita.  We now have to re-walk\n      // the contents of the would-be class to re-translate\n      // any characters that were passed through as-is\n      cs = pattern.slice(classStart + 1)\n      sp = this.parse(cs, SUBPARSE)\n      re = re.substring(0, reClassStart) + '\\\\[' + sp[0]\n      hasMagic = hasMagic || sp[1]\n    }\n\n    // handle the case where we had a +( thing at the *end*\n    // of the pattern.\n    // each pattern list stack adds 3 chars, and we need to go through\n    // and escape any | chars that were passed through as-is for the regexp.\n    // Go through and escape them, taking care not to double-escape any\n    // | chars that were already escaped.\n    for (pl = patternListStack.pop(); pl; pl = patternListStack.pop()) {\n      let tail\n      tail = re.slice(pl.reStart + pl.open.length)\n      this.debug('setting tail', re, pl)\n      // maybe some even number of \\, then maybe 1 \\, followed by a |\n      tail = tail.replace(/((?:\\\\{2}){0,64})(\\\\?)\\|/g, (_, $1, $2) => {\n        /* istanbul ignore else - should already be done */\n        if (!$2) {\n          // the | isn't already escaped, so escape it.\n          $2 = '\\\\'\n        }\n\n        // need to escape all those slashes *again*, without escaping the\n        // one that we need for escaping the | character.  As it works out,\n        // escaping an even number of slashes can be done by simply repeating\n        // it exactly after itself.  That's why this trick works.\n        //\n        // I am sorry that you have to see this.\n        return $1 + $1 + $2 + '|'\n      })\n\n      this.debug('tail=%j\\n   %s', tail, tail, pl, re)\n      const t = pl.type === '*' ? star\n        : pl.type === '?' ? qmark\n        : '\\\\' + pl.type\n\n      hasMagic = true\n      re = re.slice(0, pl.reStart) + t + '\\\\(' + tail\n    }\n\n    // handle trailing things that only matter at the very end.\n    clearStateChar()\n    if (escaping) {\n      // trailing \\\\\n      re += '\\\\\\\\'\n    }\n\n    // only need to apply the nodot start if the re starts with\n    // something that could conceivably capture a dot\n    const addPatternStart = addPatternStartSet[re.charAt(0)]\n\n    // Hack to work around lack of negative lookbehind in JS\n    // A pattern like: *.!(x).!(y|z) needs to ensure that a name\n    // like 'a.xyz.yz' doesn't match.  So, the first negative\n    // lookahead, has to look ALL the way ahead, to the end of\n    // the pattern.\n    for (let n = negativeLists.length - 1; n > -1; n--) {\n      const nl = negativeLists[n]\n\n      const nlBefore = re.slice(0, nl.reStart)\n      const nlFirst = re.slice(nl.reStart, nl.reEnd - 8)\n      let nlAfter = re.slice(nl.reEnd)\n      const nlLast = re.slice(nl.reEnd - 8, nl.reEnd) + nlAfter\n\n      // Handle nested stuff like *(*.js|!(*.json)), where open parens\n      // mean that we should *not* include the ) in the bit that is considered\n      // \"after\" the negated section.\n      const closeParensBefore = nlBefore.split(')').length\n      const openParensBefore = nlBefore.split('(').length - closeParensBefore\n      let cleanAfter = nlAfter\n      for (let i = 0; i < openParensBefore; i++) {\n        cleanAfter = cleanAfter.replace(/\\)[+*?]?/, '')\n      }\n      nlAfter = cleanAfter\n\n      const dollar = nlAfter === '' && isSub !== SUBPARSE ? '(?:$|\\\\/)' : ''\n\n      re = nlBefore + nlFirst + nlAfter + dollar + nlLast\n    }\n\n    // if the re is not \"\" at this point, then we need to make sure\n    // it doesn't match against an empty path part.\n    // Otherwise a/* will match a/, which it should not.\n    if (re !== '' && hasMagic) {\n      re = '(?=.)' + re\n    }\n\n    if (addPatternStart) {\n      re = patternStart() + re\n    }\n\n    // parsing just a piece of a larger pattern.\n    if (isSub === SUBPARSE) {\n      return [re, hasMagic]\n    }\n\n    // if it's nocase, and the lcase/uppercase don't match, it's magic\n    if (options.nocase && !hasMagic) {\n      hasMagic = pattern.toUpperCase() !== pattern.toLowerCase()\n    }\n\n    // skip the regexp for non-magical patterns\n    // unescape anything in it, though, so that it'll be\n    // an exact match against a file etc.\n    if (!hasMagic) {\n      return globUnescape(pattern)\n    }\n\n    const flags = options.nocase ? 'i' : ''\n    try {\n      return Object.assign(new RegExp('^' + re + '$', flags), {\n        _glob: pattern,\n        _src: re,\n      })\n    } catch (er) /* istanbul ignore next - should be impossible */ {\n      // If it was an invalid regular expression, then it can't match\n      // anything.  This trick looks for a character after the end of\n      // the string, which is of course impossible, except in multi-line\n      // mode, but it's not a /m regex.\n      return new RegExp('$.')\n    }\n  }\n\n  makeRe () {\n    if (this.regexp || this.regexp === false) return this.regexp\n\n    // at this point, this.set is a 2d array of partial\n    // pattern strings, or \"**\".\n    //\n    // It's better to use .match().  This function shouldn't\n    // be used, really, but it's pretty convenient sometimes,\n    // when you just want to work with a regex.\n    const set = this.set\n\n    if (!set.length) {\n      this.regexp = false\n      return this.regexp\n    }\n    const options = this.options\n\n    const twoStar = options.noglobstar ? star\n      : options.dot ? twoStarDot\n      : twoStarNoDot\n    const flags = options.nocase ? 'i' : ''\n\n    // coalesce globstars and regexpify non-globstar patterns\n    // if it's the only item, then we just do one twoStar\n    // if it's the first, and there are more, prepend (\\/|twoStar\\/)? to next\n    // if it's the last, append (\\/twoStar|) to previous\n    // if it's in the middle, append (\\/|\\/twoStar\\/) to previous\n    // then filter out GLOBSTAR symbols\n    let re = set.map(pattern => {\n      pattern = pattern.map(p =>\n        typeof p === 'string' ? regExpEscape(p)\n        : p === GLOBSTAR ? GLOBSTAR\n        : p._src\n      ).reduce((set, p) => {\n        if (!(set[set.length - 1] === GLOBSTAR && p === GLOBSTAR)) {\n          set.push(p)\n        }\n        return set\n      }, [])\n      pattern.forEach((p, i) => {\n        if (p !== GLOBSTAR || pattern[i-1] === GLOBSTAR) {\n          return\n        }\n        if (i === 0) {\n          if (pattern.length > 1) {\n            pattern[i+1] = '(?:\\\\\\/|' + twoStar + '\\\\\\/)?' + pattern[i+1]\n          } else {\n            pattern[i] = twoStar\n          }\n        } else if (i === pattern.length - 1) {\n          pattern[i-1] += '(?:\\\\\\/|' + twoStar + ')?'\n        } else {\n          pattern[i-1] += '(?:\\\\\\/|\\\\\\/' + twoStar + '\\\\\\/)' + pattern[i+1]\n          pattern[i+1] = GLOBSTAR\n        }\n      })\n      return pattern.filter(p => p !== GLOBSTAR).join('/')\n    }).join('|')\n\n    // must match entire pattern\n    // ending in a * or ** will make it less strict.\n    re = '^(?:' + re + ')$'\n\n    // can match anything, as long as it's not this.\n    if (this.negate) re = '^(?!' + re + ').*$'\n\n    try {\n      this.regexp = new RegExp(re, flags)\n    } catch (ex) /* istanbul ignore next - should be impossible */ {\n      this.regexp = false\n    }\n    return this.regexp\n  }\n\n  match (f, partial = this.partial) {\n    this.debug('match', f, this.pattern)\n    // short-circuit in the case of busted things.\n    // comments, etc.\n    if (this.comment) return false\n    if (this.empty) return f === ''\n\n    if (f === '/' && partial) return true\n\n    const options = this.options\n\n    // windows: need to use /, not \\\n    if (path.sep !== '/') {\n      f = f.split(path.sep).join('/')\n    }\n\n    // treat the test path as a set of pathparts.\n    f = f.split(slashSplit)\n    this.debug(this.pattern, 'split', f)\n\n    // just ONE of the pattern sets in this.set needs to match\n    // in order for it to be valid.  If negating, then just one\n    // match means that we have failed.\n    // Either way, return on the first hit.\n\n    const set = this.set\n    this.debug(this.pattern, 'set', set)\n\n    // Find the basename of the path by looking for the last non-empty segment\n    let filename\n    for (let i = f.length - 1; i >= 0; i--) {\n      filename = f[i]\n      if (filename) break\n    }\n\n    for (let i = 0; i < set.length; i++) {\n      const pattern = set[i]\n      let file = f\n      if (options.matchBase && pattern.length === 1) {\n        file = [filename]\n      }\n      const hit = this.matchOne(file, pattern, partial)\n      if (hit) {\n        if (options.flipNegate) return true\n        return !this.negate\n      }\n    }\n\n    // didn't get any hits.  this is success if it's a negative\n    // pattern, failure otherwise.\n    if (options.flipNegate) return false\n    return this.negate\n  }\n\n  static defaults (def) {\n    return minimatch.defaults(def).Minimatch\n  }\n}\n\nminimatch.Minimatch = Minimatch\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readdir-glob/node_modules/minimatch/minimatch.js\n");

/***/ })

};
;
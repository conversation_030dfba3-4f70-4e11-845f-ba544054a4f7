"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTicketDistributionByStage = void 0;
const helpers_1 = require("../../../../utils/helpers");
/**
 * Get ticket distribution across pipeline stages
 * Shows how many tickets are currently in each stage
 *
 * @param req - Express request object with query parameters for filtering
 * @param res - Express response object
 * @returns Distribution data with ticket counts and percentages per stage
 */
const getTicketDistributionByStage = async (req, res) => {
    try {
        const { dateFrom, dateTo, tags, assignedTo, stageId, priority, } = req.query;
        // Build where clause for tickets
        const ticketWhereClause = {
            deletedAt: null,
        };
        // Add date filtering
        if (dateFrom || dateTo) {
            ticketWhereClause.createdAt = {};
            if (dateFrom) {
                ticketWhereClause.createdAt.gte = new Date(dateFrom);
            }
            if (dateTo) {
                ticketWhereClause.createdAt.lte = new Date(dateTo);
            }
        }
        // Add tag filtering
        if (tags) {
            const tagArray = tags.split(',').map((tag) => tag.trim());
            ticketWhereClause.tags = {
                hasSome: tagArray,
            };
        }
        // Add priority filtering
        if (priority) {
            ticketWhereClause.priority = priority;
        }
        // Build where clause for ticket stages
        const stageWhereClause = {};
        if (assignedTo) {
            stageWhereClause.assignedTo = assignedTo;
        }
        if (stageId) {
            stageWhereClause.pipelineStageId = stageId;
        }
        // Get all tickets with their current stages
        const tickets = await prisma.ticket.findMany({
            where: {
                ...ticketWhereClause,
                ...(Object.keys(stageWhereClause).length > 0 && {
                    stages: {
                        some: stageWhereClause,
                    },
                }),
            },
            include: {
                stages: {
                    include: {
                        pipelineStage: true,
                    },
                    orderBy: {
                        createdAt: 'desc',
                    },
                    take: 1, // Get the most recent stage
                },
                pipeline: {
                    include: {
                        stages: {
                            orderBy: { order: 'asc' },
                        },
                    },
                },
            },
        });
        // Get all pipeline stages for the filtered tickets
        const pipelineIds = [...new Set(tickets.map(ticket => ticket.pipelineId).filter(Boolean))];
        const allStages = await prisma.pipelineStage.findMany({
            where: {
                pipelineId: {
                    in: pipelineIds,
                },
                deletedAt: null,
            },
            orderBy: [
                { pipelineId: 'asc' },
                { order: 'asc' },
            ],
        });
        // Create a map to count tickets per stage
        const stageDistribution = new Map();
        // Initialize all stages with 0 counts
        allStages.forEach(stage => {
            stageDistribution.set(stage.id, {
                stageId: stage.id,
                stageName: stage.name || 'Unnamed Stage',
                stageOrder: stage.order,
                pipelineId: stage.pipelineId || '',
                ticketCount: 0,
            });
        });
        // Count tickets in each stage by currentStageId
        tickets.forEach(ticket => {
            if (ticket.currentStageId && stageDistribution.has(ticket.currentStageId)) {
                const stageData = stageDistribution.get(ticket.currentStageId);
                stageData.ticketCount++;
            }
        });
        // Convert map to array and calculate percentages
        const totalTickets = tickets.length;
        const distributionArray = Array.from(stageDistribution.values()).map(stage => ({
            stageId: stage.stageId,
            stageName: stage.stageName,
            stageOrder: stage.stageOrder,
            ticketCount: stage.ticketCount,
            percentage: totalTickets > 0 ? Math.round((stage.ticketCount / totalTickets) * 10000) / 100 : 0,
        }));
        // Sort by stage order
        distributionArray.sort((a, b) => a.stageOrder - b.stageOrder);
        return res.status(200).json({
            success: true,
            data: distributionArray,
        });
    }
    catch (error) {
        console.error("Error in getTicketDistributionByStage:", error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getTicketDistributionByStage = getTicketDistributionByStage;
//# sourceMappingURL=distribution.js.map
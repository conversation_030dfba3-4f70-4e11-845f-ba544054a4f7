// schemas/trackSheetSchema.ts
import { z } from "zod";

// Validation for FTP Page format (e.g., '1 of 3')
export const validateFtpPageFormat = (value: string): boolean => {
  if (!value || value.trim() === "") return false;
  const ftpPageRegex = /^\d+\s+of\s+\d+$/i;
  const match = value.match(ftpPageRegex);
  if (!match) return false;
  const [currentPage, , totalPages] = value.split(/\s+of\s+/i).map(Number);
  return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;
};

// Validation for date format (DD/MM/YYYY)
export const validateDateFormat = (value: string): boolean => {
  if (!value || value.trim() === "") return true;
  const dateRegex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
  const match = value.match(dateRegex);
  if (!match) return false;
  const day = parseInt(match[1], 10);
  const month = parseInt(match[2], 10);
  const year = parseInt(match[3], 10);
  if (month < 1 || month > 12) return false;
  if (day < 1 || day > 31) return false;
  if (year < 1900 || year > 2100) return false;
  const date = new Date(year, month - 1, day);
  return date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day;
};

// Main Zod schema for a tracksheet entry
export const trackSheetSchema = z
  .object({
    associateId: z.string().min(1, "Associate is required"),
    clientId: z.string().min(1, "Client is required"),
    entries: z.array(
      z
        .object({
          company: z.string().min(1, "Company is required"),
          division: z.string().optional(),
          invoice: z.string().min(1, "Invoice is required"),
          masterInvoice: z.string().optional(),
          bol: z.string().optional(),
          invoiceDate: z
            .string()
            .min(1, "Invoice date is required")
            .refine(validateDateFormat, "Enter valid date in DD/MM/YYYY"),
          receivedDate: z
            .string()
            .min(1, "Received date is required")
            .refine(validateDateFormat, "Enter valid date in DD/MM/YYYY"),
          shipmentDate: z
            .string()
            .optional()
            .refine((v) => !v || validateDateFormat(v), "Enter valid date in DD/MM/YYYY"),
          carrierName: z.string().min(1, "Carrier name is required"),
          invoiceStatus: z.string().min(1, "Invoice status is required"),
          manualMatching: z.string().min(1, "Manual matching is required"),
          invoiceType: z.string().min(1, "Invoice type is required"),
          billToClient: z.string().optional(),
          finalInvoice: z.boolean().optional(),
          currency: z.string().min(1, "Currency is required"),
          qtyShipped: z.string().optional(),
          weightUnitName: z.string().optional(),
          quantityBilledText: z.string().optional(),
          freightClass: z.string().optional(),
          invoiceTotal: z.string().min(1, "Invoice total is required"),
          savings: z.string().optional(),
          financialNotes: z.string().optional(),
          ftpFileName: z.string().min(1, "FTP File Name is required"),
          invoiceFileId: z.string().optional(),
          ftpPage: z
            .string()
            .min(1, "FTP Page is required")
            .refine(validateFtpPageFormat, "Invalid FTP Page format"),
          docAvailable: z.array(z.string()).optional().default([]),
          otherDocuments: z.string().optional(),
          notes: z.string().optional(),
          legrandAlias: z.string().optional(),
          legrandCompanyName: z.string().optional(),
          legrandAddress: z.string().optional(),
          legrandZipcode: z.string().optional(),
          shipperAlias: z.string().optional(),
          shipperAddress: z.string().optional(),
          shipperZipcode: z.string().optional(),
          consigneeAlias: z.string().optional(),
          consigneeAddress: z.string().optional(),
          consigneeZipcode: z.string().optional(),
          billtoAlias: z.string().optional(),
          billtoAddress: z.string().optional(),
          billtoZipcode: z.string().optional(),
          shipperType: z.string().min(1, "DC/CV is required"),
          consigneeType: z.string().min(1, "DC/CV is required"),
          billtoType: z.string().min(1, "DC/CV is required"),
          legrandFreightTerms: z.string().min(1, "Freight Term is required"),
          customFields: z
            .array(
              z.object({
                id: z.string(),
                name: z.string(),
                type: z.string().optional(),
                value: z.string().optional(),
              })
            )
            .default([]),
          enteredBy: z.string().optional(),
        })
        // Entry-level refinements
        .refine(
          (entry) => {
            // Invoice date must be before or equal to received date
            if (validateDateFormat(entry.invoiceDate) && validateDateFormat(entry.receivedDate)) {
              const [invDay, invMonth, invYear] = entry.invoiceDate.split("/").map(Number);
              const [recDay, recMonth, recYear] = entry.receivedDate.split("/").map(Number);
              const invoiceDateObj = new Date(invYear, invMonth - 1, invDay);
              const receivedDateObj = new Date(recYear, recMonth - 1, recDay);
              return invoiceDateObj <= receivedDateObj;
            }
            return true;
          },
          {
            message: "The invoice date should be older than or the same as the received date.",
            path: ["invoiceDate"],
          }
        )
        .refine(
          (entry) => {
            // LEGRAND-specific required fields
            if (entry.company === "LEGRAND" || entry.company?.includes("LEGRAND")) {
              return entry.legrandFreightTerms && entry.legrandFreightTerms.trim() !== "";
            }
            return true;
          },
          {
            message: "Freight Term is required for LEGRAND clients.",
            path: ["legrandFreightTerms"],
          }
        )
        .refine(
          (entry) => {
            // DC/CV selection is required for all Legrand blocks
            if (entry.company === "LEGRAND" || entry.company?.includes("LEGRAND")) {
              return entry.shipperType && entry.consigneeType && entry.billtoType;
            }
            return true;
          },
          {
            message: "DC/CV selection is required for all Legrand blocks.",
            path: ["shipperType"],
          }
        )
    ),
  })
  // Form-level refinements for all LEGRAND entries
  .refine(
    (data) => {
      for (let i = 0; i < data.entries.length; i++) {
        const entry = data.entries[i];
        if (entry.company === "LEGRAND" || entry.company?.includes("LEGRAND")) {
          if (
            !entry.freightClass ||
            entry.freightClass.trim() === "" ||
            !entry.shipperType ||
            !entry.consigneeType ||
            !entry.billtoType ||
            !entry.legrandFreightTerms
          ) {
            return false;
          }
        }
      }
      return true;
    },
    {
      message: "Billing type and DC/CV selections are required for all Legrand blocks.",
      path: ["entries"],
    }
  );

"use client";

import { useState, useMemo, use<PERSON><PERSON>back, useEffect } from "react";
import { AgGridReact } from "ag-grid-react";
import type {
  ColDef,
  GridReadyEvent,
  SelectionChangedEvent,
} from "ag-grid-community";
import { format } from "date-fns";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Download,
  ExternalLink,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { InvoiceFileFilters } from "./InvoiceFileFilters";
import { EditInvoiceFile } from "./EditInvoiceFile";
import { DeleteInvoiceFile } from "./DeleteInvoiceFile";
import { InvoiceFileDetails } from "./InvoiceFileDetails";
import { BulkActions } from "./BulkActions";

import { InvoiceFile } from "../types";
import { getAllInvoiceFiles } from "../services/invoiceFileService";
import { ModuleRegistry, AllCommunityModule } from "ag-grid-community";
import { useInvoiceFilesContext } from "../invoiceFilesContext";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import { TicketIconCell } from "./TicketIconCell";

ModuleRegistry.registerModules([AllCommunityModule]);

interface FilterParams {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  assignedTo?: number[];
  status?: "active" | "deleted" | "all";
}

interface ViewInvoiceFilesProps {
  userData: any;
  carrier: any;
  users: any;
  permissions: any;
}

export function ViewInvoiceFiles({
  userData,
  carrier,
  users,
  permissions,
}: ViewInvoiceFilesProps) {
  const { reloadTrigger } = useInvoiceFilesContext();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [filters, setFilters] = useState<FilterParams>({});
  const [selectedRows, setSelectedRows] = useState<InvoiceFile[]>([]);
  const [editingFile, setEditingFile] = useState<InvoiceFile | null>(null);
  const [deletingFile, setDeletingFile] = useState<InvoiceFile | null>(null);
  const [viewingFile, setViewingFile] = useState<InvoiceFile | null>(null);
  const [data, setData] = useState<{
    data: InvoiceFile[];
    total: number;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getAllInvoiceFiles(page, pageSize, filters);
      if (response?.success) {
        setData({
          data: response.data || [],
          total: response.pagination?.totalRecords || 0,
        });
      } else {
        setError(response?.message || "Failed to load invoice files");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch invoice files");
    } finally {
      setIsLoading(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, pageSize, filters, reloadTrigger]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Status Badge Component
  const StatusBadge = ({ value }: { value: "active" | "deleted" }) => {
    const isDeleted = value === "deleted";
    return (
      <Badge variant={isDeleted ? "destructive" : "default"}>
        {isDeleted ? "Deleted" : "Active"}
      </Badge>
    );
  };

  // User Cell Component
  const UserCell = ({ value }: { value: any }) => {
    if (!value) {
      return <span className="text-muted-foreground">Unassigned</span>;
    }

    const initials = `${value.firstName?.[0] || ""}${
      value.lastName?.[0] || ""
    }`.toUpperCase();

    return (
      <div className="flex items-center space-x-2">
        <Avatar className="h-6 w-6">
          <AvatarImage src={value.avatar} />
          <AvatarFallback className="text-xs">{initials}</AvatarFallback>
        </Avatar>
        <span className="text-sm">
          {value.firstName} {value.lastName}
        </span>
      </div>
    );
  };

  // Actions Cell Component
  const ActionsCell = (params: { data: InvoiceFile }) => {
    const file = params.data;
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setViewingFile(file)}>
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </DropdownMenuItem>

          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["update-invoice-files"]}
          >
            <DropdownMenuItem onClick={() => setEditingFile(file)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
          </PermissionWrapper>

          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["delete-invoice-files"]}
          >
            <DropdownMenuItem
              onClick={() => setDeletingFile(file)}
              className="text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </PermissionWrapper>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  // Column Definitions
  const columnDefs: ColDef[] = useMemo(
    () => [
      {
        headerName: "Tickets",
        field: "ticketId",
        width: 80,
        suppressSizeToFit: true,
        sortable: false,
        filter: false,
        cellRenderer: (params: any) => (
          <div className="flex justify-center items-center h-full">
            <TicketIconCell ticketId={params.value} />
          </div>
        ),
      },
      {
        headerName: "Sr. No.",
        field: "srNo",
        width: 100,
        suppressSizeToFit: true,
        valueGetter: (params) => params.node?.rowIndex + 1,
      },
      {
        headerName: "Date",
        field: "date",
        sortable: true,
        filter: "agDateColumnFilter",
        width: 120,
        valueFormatter: (params) => {
          return params.value
            ? format(new Date(params.value), "MMM dd, yyyy")
            : "";
        },
      },
      {
        headerName: "File Name",
        field: "fileName",
        sortable: true,
        filter: "agTextColumnFilter",
        flex: 1,
        minWidth: 200,
      },
      {
        headerName: "Pages",
        field: "noOfPages",
        sortable: true,
        filter: "agNumberColumnFilter",
        width: 100,
        cellClass: "text-center",
      },
      {
        headerName: "Assigned To",
        field: "assignedToUser",
        sortable: true,
        width: 180,
        valueFormatter: (params) => {
          const user = params.value;
          if (!user) return "Unassigned";
          return `${user.firstName} ${user.lastName}`;
        },
        cellRenderer: (params: any) => <UserCell value={params.value} />,
      },
      {
        headerName: "Status",
        field: "status",
        sortable: true,
        width: 100,
        valueGetter: (params) => (params.data.deletedAt ? "deleted" : "active"),
        cellRenderer: (params: any) => <StatusBadge value={params.value} />,
      },
      {
        headerName: "Created",
        field: "createdAt",
        sortable: true,
        width: 120,
        valueFormatter: (params) => {
          return params.value
            ? format(new Date(params.value), "MMM dd, yyyy")
            : "";
        },
      },
      {
        headerName: "Actions",
        cellRenderer: ActionsCell,
        width: 80,
        pinned: "right",
        sortable: false,
        filter: false,
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const onGridReady = useCallback((params: GridReadyEvent) => {
    params.api.sizeColumnsToFit();
  }, []);

  const onSelectionChanged = useCallback((event: SelectionChangedEvent) => {
    setSelectedRows(event.api.getSelectedRows());
  }, []);

  const handleFilterChange = useCallback((newFilters: FilterParams) => {
    setFilters(newFilters);
    setPage(1);
  }, []);

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-destructive">
            Error loading invoice files. Please try again.
          </div>
        </CardContent>
      </Card>
    );
  }
  return (
    <div className="space-y-4">
      {/* Filters */}
      <InvoiceFileFilters onFilterChange={handleFilterChange} />

      {/* Bulk Actions */}
      {selectedRows.length > 0 && (
        <BulkActions
          userData={userData}
          users={users}
          selectedFiles={selectedRows}
          onActionComplete={() => {
            fetchData();
            setSelectedRows([]);
          }}
        />
      )}

      {/* Data Table */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Invoice Files</CardTitle>
          <div className="flex items-center space-x-2"></div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 10 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : (
            <>
              {data?.data?.some((file) => file.ticketId) && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                  <ExternalLink className="w-4 h-4 text-green-600" />
                  <span>= Ticket already exists for this file</span>
                </div>
              )}
              <div className="quartz" style={{ height: 600, width: "100%" }}>
                <AgGridReact
                  getRowId={(params) => params.data.id}
                  rowData={data?.data || []}
                  columnDefs={columnDefs}
                  defaultColDef={{
                    resizable: true,
                    sortable: true,
                    filter: true,
                  }}
                  rowSelection={{
                    mode: "multiRow",
                  }}
                  onGridReady={onGridReady}
                  onSelectionChanged={onSelectionChanged}
                  pagination={true}
                  paginationPageSize={pageSize}
                  suppressPaginationPanel={true}
                  animateRows={true}
                />
              </div>
            </>
          )}
          {/* Custom Pagination */}
          {data && (
            <div className="flex flex-wrap items-center justify-between mt-4 gap-4">
              <div className="text-sm text-muted-foreground">
                Showing {(page - 1) * pageSize + 1} to{" "}
                {Math.min(page * pageSize, data.total)} of {data.total} entries
              </div>

              <div className="flex items-center space-x-4">
                {/* Page Size Dropdown */}
                <div className="flex items-center space-x-2 text-sm">
                  <span>Rows per page:</span>
                  <select
                    className="border rounded px-2 py-1 text-sm"
                    value={pageSize}
                    onChange={(e) => {
                      setPageSize(Number(e.target.value));
                      setPage(1); // reset to first page
                    }}
                  >
                    {[10, 25, 50, 100].map((size) => (
                      <option key={size} value={size}>
                        {size}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Pagination Controls */}
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage((p) => Math.max(1, p - 1))}
                    disabled={page === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {page} of {Math.ceil(data.total / pageSize)}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage((p) => p + 1)}
                    disabled={page >= Math.ceil(data.total / pageSize)}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      <PermissionWrapper
        permissions={permissions}
        requiredPermissions={["update-invoice-files"]}
      >
        <EditInvoiceFile
          file={editingFile}
          open={!!editingFile}
          userData={userData}
          carriers={carrier}
          users={users}
          onOpenChange={(open) => !open && setEditingFile(null)}
          onSuccess={() => {
            fetchData();
            setEditingFile(null);
          }}
        />
      </PermissionWrapper>

      <PermissionWrapper
        permissions={permissions}
        requiredPermissions={["delete-invoice-files"]}
      >
        <DeleteInvoiceFile
          file={deletingFile}
          open={!!deletingFile}
          onOpenChange={(open) => !open && setDeletingFile(null)}
          onSuccess={() => {
            fetchData();
            setDeletingFile(null);
          }}
        />
      </PermissionWrapper>

      <InvoiceFileDetails
        key={viewingFile?.id}
        file={viewingFile}
        open={!!viewingFile}
        onOpenChange={(open) => {
          if (!open) {
            setViewingFile(null);
          }
        }}
        onEdit={(file) => {
          setViewingFile(null);
          setEditingFile(file);
        }}
        onDelete={(file) => {
          setViewingFile(null);
          setDeletingFile(file);
        }}
      />
    </div>
  );
}

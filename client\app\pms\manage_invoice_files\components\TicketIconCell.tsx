import { ExternalLink } from "lucide-react";
import { <PERSON><PERSON><PERSON>, Toolt<PERSON>Trigger, <PERSON>lt<PERSON><PERSON>ontent, TooltipProvider } from "@/components/ui/tooltip";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";

export const TicketIconCell = ({ ticketId }: { ticketId: string }) => {
  if (!ticketId) return null;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link
            href={`/pms/manage_tickets/${ticketId}?from=invoicefiles`}
            target="_blank"
            rel="noopener noreferrer"
          >
            <Badge
              variant="outline"
              className="flex items-center justify-center w-[22px] h-[22px] p-0.5 border-green-200 bg-green-50 text-green-700 hover:bg-green-100 cursor-pointer"
              style={{ minWidth: 22, minHeight: 22 }}
            >
              <ExternalLink className="w-4 h-4" />
            </Badge>
          </Link>
        </TooltipTrigger>
        <TooltipContent side="right" className="text-xs max-w-[180px]">
          A ticket already exists for this file.<br />
          Click to view the ticket.
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

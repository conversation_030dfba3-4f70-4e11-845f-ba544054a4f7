"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getResolutionTimeInsights = exports.getClosureRateInsights = void 0;
const helpers_1 = require("../../../../utils/helpers");
/**
 * Get closure rate insights with performance categorization
 * Provides actionable insights on stage performance
 *
 * @param req - Express request object with query parameters for filtering
 * @param res - Express response object
 * @returns Insights data with performance categories and recommendations
 */
const getClosureRateInsights = async (req, res) => {
    try {
        const { dateFrom, dateTo, tags, assignedTo, stageId, priority, } = req.query;
        // Build where clause for tickets
        const ticketWhereClause = {
            deletedAt: null,
        };
        // Add filtering
        if (dateFrom || dateTo) {
            ticketWhereClause.createdAt = {};
            if (dateFrom)
                ticketWhereClause.createdAt.gte = new Date(dateFrom);
            if (dateTo)
                ticketWhereClause.createdAt.lte = new Date(dateTo);
        }
        if (tags) {
            const tagArray = tags.split(',').map((tag) => tag.trim());
            ticketWhereClause.tags = { hasSome: tagArray };
        }
        if (priority) {
            ticketWhereClause.priority = priority;
        }
        const stageWhereClause = {};
        if (assignedTo)
            stageWhereClause.assignedTo = assignedTo;
        if (stageId)
            stageWhereClause.pipelineStageId = stageId;
        // Get tickets with stages
        const tickets = await prisma.ticket.findMany({
            where: {
                ...ticketWhereClause,
                ...(Object.keys(stageWhereClause).length > 0 && {
                    stages: { some: stageWhereClause },
                }),
            },
            include: {
                stages: {
                    include: { pipelineStage: true },
                    orderBy: { createdAt: 'asc' },
                },
                pipeline: {
                    include: {
                        stages: { orderBy: { order: 'asc' } },
                    },
                },
            },
        });
        // Get unique stages
        const allStages = new Map();
        tickets.forEach(ticket => {
            if (ticket.pipeline?.stages) {
                ticket.pipeline.stages.forEach(stage => {
                    allStages.set(stage.id, stage);
                });
            }
        });
        // Calculate closure rates for each stage
        const stageClosureRates = Array.from(allStages.values()).map(stage => {
            let ticketsEntered = 0;
            let ticketsCompleted = 0;
            tickets.forEach(ticket => {
                if (!ticket.pipeline?.stages)
                    return;
                // Sort ticket stages by createdAt
                const ticketStages = ticket.stages.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
                const stageIndex = ticket.pipeline.stages.findIndex(s => s.id === stage.id);
                const hasEnteredStage = ticketStages.some(ts => ts.pipelineStageId === stage.id);
                if (hasEnteredStage) {
                    ticketsEntered++;
                    const nextStageIndex = stageIndex + 1;
                    if (nextStageIndex < ticket.pipeline.stages.length) {
                        const nextStageId = ticket.pipeline.stages[nextStageIndex].id;
                        // Only count as completed if ticket has moved to the next stage and is not currently in this stage
                        const thisStageEntryIndex = ticketStages.findIndex(ts => ts.pipelineStageId === stage.id);
                        const movedToNext = ticketStages.slice(thisStageEntryIndex + 1).some(ts => ts.pipelineStageId === nextStageId);
                        if (movedToNext && ticket.currentStageId !== stage.id)
                            ticketsCompleted++;
                    }
                    else {
                        // Final stage: only count as completed if the ticket is currently in this stage (using currentStageId)
                        if (ticket.currentStageId === stage.id)
                            ticketsCompleted++;
                    }
                }
            });
            const closureRate = ticketsEntered > 0 ? (ticketsCompleted / ticketsEntered) * 100 : 0;
            return {
                stageId: stage.id,
                stageName: stage.name || 'Unnamed Stage',
                stageOrder: stage.order,
                closureRate: Math.round(closureRate * 100) / 100,
                ticketsEntered,
                ticketsCompleted,
            };
        });
        // Calculate overall closure rate
        const totalEntered = stageClosureRates.reduce((sum, stage) => sum + stage.ticketsEntered, 0);
        const totalCompleted = stageClosureRates.reduce((sum, stage) => sum + stage.ticketsCompleted, 0);
        const overallClosureRate = totalEntered > 0 ? (totalCompleted / totalEntered) * 100 : 0;
        // Categorize stages by performance
        const categorizePerformance = (closureRate) => {
            if (closureRate >= 85)
                return 'HIGH_PERFORMER';
            if (closureRate >= 65)
                return 'MEDIUM_PERFORMER';
            return 'LOW_PERFORMER';
        };
        const getRecommendation = (category, closureRate) => {
            switch (category) {
                case 'HIGH_PERFORMER':
                    return 'Excellent performance, maintain current processes';
                case 'MEDIUM_PERFORMER':
                    return 'Consider process improvements or additional resources';
                case 'LOW_PERFORMER':
                    return 'Requires immediate attention and process review';
                default:
                    return 'Review performance metrics';
            }
        };
        // Group stages by performance category
        const insights = {
            HIGH_PERFORMER: [],
            MEDIUM_PERFORMER: [],
            LOW_PERFORMER: [],
        };
        stageClosureRates.forEach(stage => {
            const category = categorizePerformance(stage.closureRate);
            insights[category].push({
                stageId: stage.stageId,
                stageName: stage.stageName,
                closureRate: stage.closureRate,
                recommendation: getRecommendation(category, stage.closureRate),
            });
        });
        // Format response
        const insightsArray = Object.entries(insights).map(([category, stages]) => ({
            category,
            stages,
        }));
        return res.status(200).json({
            success: true,
            data: {
                overallClosureRate: Math.round(overallClosureRate * 100) / 100,
                insights: insightsArray,
            },
        });
    }
    catch (error) {
        console.error("Error in getClosureRateInsights:", error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getClosureRateInsights = getClosureRateInsights;
/**
 * Get resolution time insights with performance analysis
 * Provides analysis of ticket resolution times by priority and performance benchmarks
 *
 * @param req - Express request object with query parameters for filtering
 * @param res - Express response object
 * @returns Resolution time analysis with performance categorization and recommendations
 */
const getResolutionTimeInsights = async (req, res) => {
    try {
        const { dateFrom, dateTo, tags, assignedTo, stageId, priority, corporationId, } = req.query;
        // Build where clause for tickets
        const ticketWhereClause = {
            deletedAt: null,
        };
        // Add filtering
        if (dateFrom || dateTo) {
            ticketWhereClause.createdAt = {};
            if (dateFrom)
                ticketWhereClause.createdAt.gte = new Date(dateFrom);
            if (dateTo)
                ticketWhereClause.createdAt.lte = new Date(dateTo);
        }
        if (tags) {
            const tagArray = tags.split(',').map((tag) => tag.trim());
            ticketWhereClause.tags = { hasSome: tagArray };
        }
        if (priority) {
            ticketWhereClause.priority = priority;
        }
        if (corporationId) {
            ticketWhereClause.pipeline = { corporationId: parseInt(corporationId) };
        }
        const stageWhereClause = {};
        if (assignedTo)
            stageWhereClause.assignedTo = assignedTo;
        if (stageId)
            stageWhereClause.pipelineStageId = stageId;
        // Get resolved tickets (tickets that reached final stage)
        const tickets = await prisma.ticket.findMany({
            where: {
                ...ticketWhereClause,
                ...(Object.keys(stageWhereClause).length > 0 && {
                    stages: { some: stageWhereClause },
                }),
            },
            include: {
                stages: {
                    include: { pipelineStage: true },
                    orderBy: { createdAt: 'desc' },
                    take: 1,
                },
                pipeline: {
                    include: {
                        stages: {
                            orderBy: { order: 'desc' },
                            take: 1,
                        },
                    },
                },
            },
        });
        // Filter to only resolved tickets and calculate resolution times
        const resolvedTickets = tickets
            .filter(ticket => {
            if (!ticket.pipeline?.stages?.length || !ticket.stages?.length)
                return false;
            const finalStage = ticket.pipeline.stages[0];
            const currentStage = ticket.stages[0];
            return currentStage.pipelineStage?.order === finalStage.order;
        })
            .map(ticket => {
            const createdAt = new Date(ticket.createdAt);
            const resolvedAt = new Date(ticket.stages[0].createdAt);
            const resolutionTimeHours = (resolvedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
            return {
                ...ticket,
                resolutionTimeHours: Math.max(0, resolutionTimeHours),
            };
        });
        // Calculate overall statistics
        const allResolutionTimes = resolvedTickets.map(t => t.resolutionTimeHours);
        const overallAverage = allResolutionTimes.length > 0
            ? allResolutionTimes.reduce((sum, time) => sum + time, 0) / allResolutionTimes.length
            : 0;
        const sortedTimes = [...allResolutionTimes].sort((a, b) => a - b);
        const overallMedian = sortedTimes.length > 0
            ? sortedTimes.length % 2 === 0
                ? (sortedTimes[Math.floor(sortedTimes.length / 2) - 1] + sortedTimes[Math.floor(sortedTimes.length / 2)]) / 2
                : sortedTimes[Math.floor(sortedTimes.length / 2)]
            : 0;
        // Analyze by priority
        const priorities = ['HIGH', 'MEDIUM', 'LOW'];
        const benchmarks = { HIGH: 72, MEDIUM: 120, LOW: 168 }; // Hours
        const priorityAnalysis = priorities.map(priorityLevel => {
            const priorityTickets = resolvedTickets.filter(t => t.priority === priorityLevel);
            const priorityTimes = priorityTickets.map(t => t.resolutionTimeHours);
            const average = priorityTimes.length > 0
                ? priorityTimes.reduce((sum, time) => sum + time, 0) / priorityTimes.length
                : 0;
            const sortedPriorityTimes = [...priorityTimes].sort((a, b) => a - b);
            const median = sortedPriorityTimes.length > 0
                ? sortedPriorityTimes.length % 2 === 0
                    ? (sortedPriorityTimes[Math.floor(sortedPriorityTimes.length / 2) - 1] + sortedPriorityTimes[Math.floor(sortedPriorityTimes.length / 2)]) / 2
                    : sortedPriorityTimes[Math.floor(sortedPriorityTimes.length / 2)]
                : 0;
            const benchmark = benchmarks[priorityLevel];
            const performance = average <= benchmark ? 'GOOD' : average <= benchmark * 1.5 ? 'AVERAGE' : 'SLOW';
            return {
                priority: priorityLevel,
                averageTime: Math.round(average * 100) / 100,
                medianTime: Math.round(median * 100) / 100,
                sampleSize: priorityTimes.length,
                performance,
                benchmark,
            };
        });
        // Generate recommendations
        const recommendations = [];
        priorityAnalysis.forEach(analysis => {
            switch (analysis.performance) {
                case 'GOOD':
                    recommendations.push(`${analysis.priority.toLowerCase()} priority tickets are meeting SLA requirements`);
                    break;
                case 'AVERAGE':
                    recommendations.push(`${analysis.priority.toLowerCase()} priority tickets performance is within acceptable range`);
                    break;
                case 'SLOW':
                    recommendations.push(`${analysis.priority.toLowerCase()} priority tickets are taking longer than benchmark - consider resource allocation`);
                    break;
            }
        });
        if (recommendations.length === 0) {
            recommendations.push('No specific recommendations available based on current data');
        }
        return res.status(200).json({
            success: true,
            data: {
                overallStats: {
                    averageResolutionTime: Math.round(overallAverage * 100) / 100,
                    medianResolutionTime: Math.round(overallMedian * 100) / 100,
                    timeUnit: "hours",
                },
                byPriority: priorityAnalysis,
                recommendations,
            },
        });
    }
    catch (error) {
        console.error("Error in getResolutionTimeInsights:", error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getResolutionTimeInsights = getResolutionTimeInsights;
//# sourceMappingURL=insights.js.map
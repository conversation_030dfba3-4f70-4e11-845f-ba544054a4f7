/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/buffers";
exports.ids = ["vendor-chunks/buffers"];
exports.modules = {

/***/ "(ssr)/./node_modules/buffers/index.js":
/*!***************************************!*\
  !*** ./node_modules/buffers/index.js ***!
  \***************************************/
/***/ ((module) => {

eval("module.exports = Buffers;\n\nfunction Buffers (bufs) {\n    if (!(this instanceof Buffers)) return new Buffers(bufs);\n    this.buffers = bufs || [];\n    this.length = this.buffers.reduce(function (size, buf) {\n        return size + buf.length\n    }, 0);\n}\n\nBuffers.prototype.push = function () {\n    for (var i = 0; i < arguments.length; i++) {\n        if (!Buffer.isBuffer(arguments[i])) {\n            throw new TypeError('Tried to push a non-buffer');\n        }\n    }\n    \n    for (var i = 0; i < arguments.length; i++) {\n        var buf = arguments[i];\n        this.buffers.push(buf);\n        this.length += buf.length;\n    }\n    return this.length;\n};\n\nBuffers.prototype.unshift = function () {\n    for (var i = 0; i < arguments.length; i++) {\n        if (!Buffer.isBuffer(arguments[i])) {\n            throw new TypeError('Tried to unshift a non-buffer');\n        }\n    }\n    \n    for (var i = 0; i < arguments.length; i++) {\n        var buf = arguments[i];\n        this.buffers.unshift(buf);\n        this.length += buf.length;\n    }\n    return this.length;\n};\n\nBuffers.prototype.copy = function (dst, dStart, start, end) {\n    return this.slice(start, end).copy(dst, dStart, 0, end - start);\n};\n\nBuffers.prototype.splice = function (i, howMany) {\n    var buffers = this.buffers;\n    var index = i >= 0 ? i : this.length - i;\n    var reps = [].slice.call(arguments, 2);\n    \n    if (howMany === undefined) {\n        howMany = this.length - index;\n    }\n    else if (howMany > this.length - index) {\n        howMany = this.length - index;\n    }\n    \n    for (var i = 0; i < reps.length; i++) {\n        this.length += reps[i].length;\n    }\n    \n    var removed = new Buffers();\n    var bytes = 0;\n    \n    var startBytes = 0;\n    for (\n        var ii = 0;\n        ii < buffers.length && startBytes + buffers[ii].length < index;\n        ii ++\n    ) { startBytes += buffers[ii].length }\n    \n    if (index - startBytes > 0) {\n        var start = index - startBytes;\n        \n        if (start + howMany < buffers[ii].length) {\n            removed.push(buffers[ii].slice(start, start + howMany));\n            \n            var orig = buffers[ii];\n            //var buf = new Buffer(orig.length - howMany);\n            var buf0 = new Buffer(start);\n            for (var i = 0; i < start; i++) {\n                buf0[i] = orig[i];\n            }\n            \n            var buf1 = new Buffer(orig.length - start - howMany);\n            for (var i = start + howMany; i < orig.length; i++) {\n                buf1[ i - howMany - start ] = orig[i]\n            }\n            \n            if (reps.length > 0) {\n                var reps_ = reps.slice();\n                reps_.unshift(buf0);\n                reps_.push(buf1);\n                buffers.splice.apply(buffers, [ ii, 1 ].concat(reps_));\n                ii += reps_.length;\n                reps = [];\n            }\n            else {\n                buffers.splice(ii, 1, buf0, buf1);\n                //buffers[ii] = buf;\n                ii += 2;\n            }\n        }\n        else {\n            removed.push(buffers[ii].slice(start));\n            buffers[ii] = buffers[ii].slice(0, start);\n            ii ++;\n        }\n    }\n    \n    if (reps.length > 0) {\n        buffers.splice.apply(buffers, [ ii, 0 ].concat(reps));\n        ii += reps.length;\n    }\n    \n    while (removed.length < howMany) {\n        var buf = buffers[ii];\n        var len = buf.length;\n        var take = Math.min(len, howMany - removed.length);\n        \n        if (take === len) {\n            removed.push(buf);\n            buffers.splice(ii, 1);\n        }\n        else {\n            removed.push(buf.slice(0, take));\n            buffers[ii] = buffers[ii].slice(take);\n        }\n    }\n    \n    this.length -= removed.length;\n    \n    return removed;\n};\n \nBuffers.prototype.slice = function (i, j) {\n    var buffers = this.buffers;\n    if (j === undefined) j = this.length;\n    if (i === undefined) i = 0;\n    \n    if (j > this.length) j = this.length;\n    \n    var startBytes = 0;\n    for (\n        var si = 0;\n        si < buffers.length && startBytes + buffers[si].length <= i;\n        si ++\n    ) { startBytes += buffers[si].length }\n    \n    var target = new Buffer(j - i);\n    \n    var ti = 0;\n    for (var ii = si; ti < j - i && ii < buffers.length; ii++) {\n        var len = buffers[ii].length;\n        \n        var start = ti === 0 ? i - startBytes : 0;\n        var end = ti + len >= j - i\n            ? Math.min(start + (j - i) - ti, len)\n            : len\n        ;\n        \n        buffers[ii].copy(target, ti, start, end);\n        ti += end - start;\n    }\n    \n    return target;\n};\n\nBuffers.prototype.pos = function (i) {\n    if (i < 0 || i >= this.length) throw new Error('oob');\n    var l = i, bi = 0, bu = null;\n    for (;;) {\n        bu = this.buffers[bi];\n        if (l < bu.length) {\n            return {buf: bi, offset: l};\n        } else {\n            l -= bu.length;\n        }\n        bi++;\n    }\n};\n\nBuffers.prototype.get = function get (i) {\n    var pos = this.pos(i);\n\n    return this.buffers[pos.buf].get(pos.offset);\n};\n\nBuffers.prototype.set = function set (i, b) {\n    var pos = this.pos(i);\n\n    return this.buffers[pos.buf].set(pos.offset, b);\n};\n\nBuffers.prototype.indexOf = function (needle, offset) {\n    if (\"string\" === typeof needle) {\n        needle = new Buffer(needle);\n    } else if (needle instanceof Buffer) {\n        // already a buffer\n    } else {\n        throw new Error('Invalid type for a search string');\n    }\n\n    if (!needle.length) {\n        return 0;\n    }\n\n    if (!this.length) {\n        return -1;\n    }\n\n    var i = 0, j = 0, match = 0, mstart, pos = 0;\n\n    // start search from a particular point in the virtual buffer\n    if (offset) {\n        var p = this.pos(offset);\n        i = p.buf;\n        j = p.offset;\n        pos = offset;\n    }\n\n    // for each character in virtual buffer\n    for (;;) {\n        while (j >= this.buffers[i].length) {\n            j = 0;\n            i++;\n\n            if (i >= this.buffers.length) {\n                // search string not found\n                return -1;\n            }\n        }\n\n        var char = this.buffers[i][j];\n\n        if (char == needle[match]) {\n            // keep track where match started\n            if (match == 0) {\n                mstart = {\n                    i: i,\n                    j: j,\n                    pos: pos\n                };\n            }\n            match++;\n            if (match == needle.length) {\n                // full match\n                return mstart.pos;\n            }\n        } else if (match != 0) {\n            // a partial match ended, go back to match starting position\n            // this will continue the search at the next character\n            i = mstart.i;\n            j = mstart.j;\n            pos = mstart.pos;\n            match = 0;\n        }\n\n        j++;\n        pos++;\n    }\n};\n\nBuffers.prototype.toBuffer = function() {\n    return this.slice();\n}\n\nBuffers.prototype.toString = function(encoding, start, end) {\n    return this.slice(start, end).toString(encoding);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffers/index.js\n");

/***/ })

};
;
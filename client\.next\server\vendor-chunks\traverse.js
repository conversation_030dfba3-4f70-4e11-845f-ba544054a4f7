/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/traverse";
exports.ids = ["vendor-chunks/traverse"];
exports.modules = {

/***/ "(ssr)/./node_modules/traverse/index.js":
/*!****************************************!*\
  !*** ./node_modules/traverse/index.js ***!
  \****************************************/
/***/ ((module) => {

eval("module.exports = Traverse;\nfunction Traverse (obj) {\n    if (!(this instanceof Traverse)) return new Traverse(obj);\n    this.value = obj;\n}\n\nTraverse.prototype.get = function (ps) {\n    var node = this.value;\n    for (var i = 0; i < ps.length; i ++) {\n        var key = ps[i];\n        if (!Object.hasOwnProperty.call(node, key)) {\n            node = undefined;\n            break;\n        }\n        node = node[key];\n    }\n    return node;\n};\n\nTraverse.prototype.set = function (ps, value) {\n    var node = this.value;\n    for (var i = 0; i < ps.length - 1; i ++) {\n        var key = ps[i];\n        if (!Object.hasOwnProperty.call(node, key)) node[key] = {};\n        node = node[key];\n    }\n    node[ps[i]] = value;\n    return value;\n};\n\nTraverse.prototype.map = function (cb) {\n    return walk(this.value, cb, true);\n};\n\nTraverse.prototype.forEach = function (cb) {\n    this.value = walk(this.value, cb, false);\n    return this.value;\n};\n\nTraverse.prototype.reduce = function (cb, init) {\n    var skip = arguments.length === 1;\n    var acc = skip ? this.value : init;\n    this.forEach(function (x) {\n        if (!this.isRoot || !skip) {\n            acc = cb.call(this, acc, x);\n        }\n    });\n    return acc;\n};\n\nTraverse.prototype.deepEqual = function (obj) {\n    if (arguments.length !== 1) {\n        throw new Error(\n            'deepEqual requires exactly one object to compare against'\n        );\n    }\n    \n    var equal = true;\n    var node = obj;\n    \n    this.forEach(function (y) {\n        var notEqual = (function () {\n            equal = false;\n            //this.stop();\n            return undefined;\n        }).bind(this);\n        \n        //if (node === undefined || node === null) return notEqual();\n        \n        if (!this.isRoot) {\n        /*\n            if (!Object.hasOwnProperty.call(node, this.key)) {\n                return notEqual();\n            }\n        */\n            if (typeof node !== 'object') return notEqual();\n            node = node[this.key];\n        }\n        \n        var x = node;\n        \n        this.post(function () {\n            node = x;\n        });\n        \n        var toS = function (o) {\n            return Object.prototype.toString.call(o);\n        };\n        \n        if (this.circular) {\n            if (Traverse(obj).get(this.circular.path) !== x) notEqual();\n        }\n        else if (typeof x !== typeof y) {\n            notEqual();\n        }\n        else if (x === null || y === null || x === undefined || y === undefined) {\n            if (x !== y) notEqual();\n        }\n        else if (x.__proto__ !== y.__proto__) {\n            notEqual();\n        }\n        else if (x === y) {\n            // nop\n        }\n        else if (typeof x === 'function') {\n            if (x instanceof RegExp) {\n                // both regexps on account of the __proto__ check\n                if (x.toString() != y.toString()) notEqual();\n            }\n            else if (x !== y) notEqual();\n        }\n        else if (typeof x === 'object') {\n            if (toS(y) === '[object Arguments]'\n            || toS(x) === '[object Arguments]') {\n                if (toS(x) !== toS(y)) {\n                    notEqual();\n                }\n            }\n            else if (x instanceof Date || y instanceof Date) {\n                if (!(x instanceof Date) || !(y instanceof Date)\n                || x.getTime() !== y.getTime()) {\n                    notEqual();\n                }\n            }\n            else {\n                var kx = Object.keys(x);\n                var ky = Object.keys(y);\n                if (kx.length !== ky.length) return notEqual();\n                for (var i = 0; i < kx.length; i++) {\n                    var k = kx[i];\n                    if (!Object.hasOwnProperty.call(y, k)) {\n                        notEqual();\n                    }\n                }\n            }\n        }\n    });\n    \n    return equal;\n};\n\nTraverse.prototype.paths = function () {\n    var acc = [];\n    this.forEach(function (x) {\n        acc.push(this.path); \n    });\n    return acc;\n};\n\nTraverse.prototype.nodes = function () {\n    var acc = [];\n    this.forEach(function (x) {\n        acc.push(this.node);\n    });\n    return acc;\n};\n\nTraverse.prototype.clone = function () {\n    var parents = [], nodes = [];\n    \n    return (function clone (src) {\n        for (var i = 0; i < parents.length; i++) {\n            if (parents[i] === src) {\n                return nodes[i];\n            }\n        }\n        \n        if (typeof src === 'object' && src !== null) {\n            var dst = copy(src);\n            \n            parents.push(src);\n            nodes.push(dst);\n            \n            Object.keys(src).forEach(function (key) {\n                dst[key] = clone(src[key]);\n            });\n            \n            parents.pop();\n            nodes.pop();\n            return dst;\n        }\n        else {\n            return src;\n        }\n    })(this.value);\n};\n\nfunction walk (root, cb, immutable) {\n    var path = [];\n    var parents = [];\n    var alive = true;\n    \n    return (function walker (node_) {\n        var node = immutable ? copy(node_) : node_;\n        var modifiers = {};\n        \n        var state = {\n            node : node,\n            node_ : node_,\n            path : [].concat(path),\n            parent : parents.slice(-1)[0],\n            key : path.slice(-1)[0],\n            isRoot : path.length === 0,\n            level : path.length,\n            circular : null,\n            update : function (x) {\n                if (!state.isRoot) {\n                    state.parent.node[state.key] = x;\n                }\n                state.node = x;\n            },\n            'delete' : function () {\n                delete state.parent.node[state.key];\n            },\n            remove : function () {\n                if (Array.isArray(state.parent.node)) {\n                    state.parent.node.splice(state.key, 1);\n                }\n                else {\n                    delete state.parent.node[state.key];\n                }\n            },\n            before : function (f) { modifiers.before = f },\n            after : function (f) { modifiers.after = f },\n            pre : function (f) { modifiers.pre = f },\n            post : function (f) { modifiers.post = f },\n            stop : function () { alive = false }\n        };\n        \n        if (!alive) return state;\n        \n        if (typeof node === 'object' && node !== null) {\n            state.isLeaf = Object.keys(node).length == 0;\n            \n            for (var i = 0; i < parents.length; i++) {\n                if (parents[i].node_ === node_) {\n                    state.circular = parents[i];\n                    break;\n                }\n            }\n        }\n        else {\n            state.isLeaf = true;\n        }\n        \n        state.notLeaf = !state.isLeaf;\n        state.notRoot = !state.isRoot;\n        \n        // use return values to update if defined\n        var ret = cb.call(state, state.node);\n        if (ret !== undefined && state.update) state.update(ret);\n        if (modifiers.before) modifiers.before.call(state, state.node);\n        \n        if (typeof state.node == 'object'\n        && state.node !== null && !state.circular) {\n            parents.push(state);\n            \n            var keys = Object.keys(state.node);\n            keys.forEach(function (key, i) {\n                path.push(key);\n                \n                if (modifiers.pre) modifiers.pre.call(state, state.node[key], key);\n                \n                var child = walker(state.node[key]);\n                if (immutable && Object.hasOwnProperty.call(state.node, key)) {\n                    state.node[key] = child.node;\n                }\n                \n                child.isLast = i == keys.length - 1;\n                child.isFirst = i == 0;\n                \n                if (modifiers.post) modifiers.post.call(state, child);\n                \n                path.pop();\n            });\n            parents.pop();\n        }\n        \n        if (modifiers.after) modifiers.after.call(state, state.node);\n        \n        return state;\n    })(root).node;\n}\n\nObject.keys(Traverse.prototype).forEach(function (key) {\n    Traverse[key] = function (obj) {\n        var args = [].slice.call(arguments, 1);\n        var t = Traverse(obj);\n        return t[key].apply(t, args);\n    };\n});\n\nfunction copy (src) {\n    if (typeof src === 'object' && src !== null) {\n        var dst;\n        \n        if (Array.isArray(src)) {\n            dst = [];\n        }\n        else if (src instanceof Date) {\n            dst = new Date(src);\n        }\n        else if (src instanceof Boolean) {\n            dst = new Boolean(src);\n        }\n        else if (src instanceof Number) {\n            dst = new Number(src);\n        }\n        else if (src instanceof String) {\n            dst = new String(src);\n        }\n        else {\n            dst = Object.create(Object.getPrototypeOf(src));\n        }\n        \n        Object.keys(src).forEach(function (key) {\n            dst[key] = src[key];\n        });\n        return dst;\n    }\n    else return src;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/traverse/index.js\n");

/***/ })

};
;
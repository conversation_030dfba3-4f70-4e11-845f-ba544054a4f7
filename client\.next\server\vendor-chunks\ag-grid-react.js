"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ag-grid-react";
exports.ids = ["vendor-chunks/ag-grid-react"];
exports.modules = {

/***/ "(ssr)/./node_modules/ag-grid-react/dist/package/index.esm.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/ag-grid-react/dist/package/index.esm.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgGridReact: () => (/* binding */ AgGridReact),\n/* harmony export */   CustomComponentContext: () => (/* binding */ CustomContext),\n/* harmony export */   getInstance: () => (/* binding */ getInstance),\n/* harmony export */   useGridCellEditor: () => (/* binding */ useGridCellEditor),\n/* harmony export */   useGridDate: () => (/* binding */ useGridDate),\n/* harmony export */   useGridFilter: () => (/* binding */ useGridFilter),\n/* harmony export */   useGridFilterDisplay: () => (/* binding */ useGridFilterDisplay),\n/* harmony export */   useGridFloatingFilter: () => (/* binding */ useGridFloatingFilter),\n/* harmony export */   useGridMenuItem: () => (/* binding */ useGridMenuItem),\n/* harmony export */   warnReactiveCustomComponents: () => (/* binding */ warnReactiveCustomComponents)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var ag_grid_community__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ag-grid-community */ \"(ssr)/./node_modules/ag-grid-community/dist/package/main.esm.mjs\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n// packages/ag-grid-react/src/agGridReact.tsx\n\n\n// packages/ag-grid-react/src/reactUi/agGridReactUi.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/cellRenderer/groupCellRenderer.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/beansContext.tsx\n\nvar BeansContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext({});\nvar RenderModeContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(\"default\");\n\n// packages/ag-grid-react/src/reactUi/jsComp.tsx\nvar showJsComp = (compDetails, context, eParent, ref) => {\n  const doNothing = !compDetails || compDetails.componentFromFramework || context.isDestroyed();\n  if (doNothing) {\n    return;\n  }\n  const promise = compDetails.newAgStackInstance();\n  let comp;\n  let compGui;\n  let destroyed = false;\n  promise.then((c) => {\n    if (destroyed) {\n      context.destroyBean(c);\n      return;\n    }\n    comp = c;\n    compGui = comp.getGui();\n    eParent.appendChild(compGui);\n    setRef(ref, comp);\n  });\n  return () => {\n    destroyed = true;\n    if (!comp) {\n      return;\n    }\n    compGui?.parentElement?.removeChild(compGui);\n    context.destroyBean(comp);\n    if (ref) {\n      setRef(ref, void 0);\n    }\n  };\n};\nvar setRef = (ref, value) => {\n  if (!ref) {\n    return;\n  }\n  if (ref instanceof Function) {\n    const refCallback = ref;\n    refCallback(value);\n  } else {\n    const refObj = ref;\n    refObj.current = value;\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/utils.tsx\n\n\nvar classesList = (...list) => {\n  const filtered = list.filter((s) => s != null && s !== \"\");\n  return filtered.join(\" \");\n};\nvar CssClasses = class _CssClasses {\n  constructor(...initialClasses) {\n    this.classesMap = {};\n    initialClasses.forEach((className) => {\n      this.classesMap[className] = true;\n    });\n  }\n  setClass(className, on) {\n    const nothingHasChanged = !!this.classesMap[className] == on;\n    if (nothingHasChanged) {\n      return this;\n    }\n    const res = new _CssClasses();\n    res.classesMap = { ...this.classesMap };\n    res.classesMap[className] = on;\n    return res;\n  }\n  toString() {\n    const res = Object.keys(this.classesMap).filter((key) => this.classesMap[key]).join(\" \");\n    return res;\n  }\n};\nvar isComponentStateless = (Component2) => {\n  const hasSymbol = () => typeof Symbol === \"function\" && Symbol.for;\n  const getMemoType = () => hasSymbol() ? Symbol.for(\"react.memo\") : 60115;\n  return typeof Component2 === \"function\" && !(Component2.prototype && Component2.prototype.isReactComponent) || typeof Component2 === \"object\" && Component2.$$typeof === getMemoType();\n};\nvar reactVersion = react__WEBPACK_IMPORTED_MODULE_0__.version?.split(\".\")[0];\nvar isReactVersion17Minus = reactVersion === \"16\" || reactVersion === \"17\";\nfunction isReact19() {\n  return reactVersion === \"19\";\n}\nvar disableFlushSync = false;\nfunction runWithoutFlushSync(func) {\n  if (!disableFlushSync) {\n    setTimeout(() => disableFlushSync = false, 0);\n  }\n  disableFlushSync = true;\n  return func();\n}\nvar agFlushSync = (useFlushSync, fn) => {\n  if (!isReactVersion17Minus && useFlushSync && !disableFlushSync) {\n    react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(fn);\n  } else {\n    fn();\n  }\n};\nvar agStartTransition = (fn) => {\n  if (!isReactVersion17Minus) {\n    react__WEBPACK_IMPORTED_MODULE_0__.startTransition(fn);\n  } else {\n    fn();\n  }\n};\nfunction agUseSyncExternalStore(subscribe, getSnapshot, defaultSnapshot) {\n  if (react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(subscribe, getSnapshot);\n  } else {\n    return defaultSnapshot;\n  }\n}\nfunction getNextValueIfDifferent(prev, next, maintainOrder) {\n  if (next == null || prev == null) {\n    return next;\n  }\n  if (prev === next || next.length === 0 && prev.length === 0) {\n    return prev;\n  }\n  if (maintainOrder || prev.length === 0 && next.length > 0 || prev.length > 0 && next.length === 0) {\n    return next;\n  }\n  const oldValues = [];\n  const newValues = [];\n  const prevMap = /* @__PURE__ */ new Map();\n  const nextMap = /* @__PURE__ */ new Map();\n  for (let i = 0; i < next.length; i++) {\n    const c = next[i];\n    nextMap.set(c.instanceId, c);\n  }\n  for (let i = 0; i < prev.length; i++) {\n    const c = prev[i];\n    prevMap.set(c.instanceId, c);\n    if (nextMap.has(c.instanceId)) {\n      oldValues.push(c);\n    }\n  }\n  for (let i = 0; i < next.length; i++) {\n    const c = next[i];\n    const instanceId = c.instanceId;\n    if (!prevMap.has(instanceId)) {\n      newValues.push(c);\n    }\n  }\n  if (oldValues.length === prev.length && newValues.length === 0) {\n    return prev;\n  }\n  if (oldValues.length === 0 && newValues.length === next.length) {\n    return next;\n  }\n  if (oldValues.length === 0) {\n    return newValues;\n  }\n  if (newValues.length === 0) {\n    return oldValues;\n  }\n  return [...oldValues, ...newValues];\n}\n\n// packages/ag-grid-react/src/reactUi/cellRenderer/groupCellRenderer.tsx\nvar GroupCellRenderer = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const { registry, context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eValueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eCheckboxRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eExpandedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eContractedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const ctrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [innerCompDetails, setInnerCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [childCount, setChildCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [expandedCssClasses, setExpandedCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses(\"ag-hidden\"));\n  const [contractedCssClasses, setContractedCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses(\"ag-hidden\"));\n  const [checkboxCssClasses, setCheckboxCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses(\"ag-invisible\"));\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => {\n    return {\n      // force new instance when grid tries to refresh\n      refresh() {\n        return false;\n      }\n    };\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    return showJsComp(innerCompDetails, context, eValueRef.current);\n  }, [innerCompDetails]);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    if (!eRef) {\n      ctrlRef.current = context.destroyBean(ctrlRef.current);\n      return;\n    }\n    const compProxy = {\n      setInnerRenderer: (details, valueToDisplay) => {\n        setInnerCompDetails(details);\n        setValue(valueToDisplay);\n      },\n      setChildCount: (count) => setChildCount(count),\n      toggleCss: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setContractedDisplayed: (displayed) => setContractedCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed)),\n      setExpandedDisplayed: (displayed) => setExpandedCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed)),\n      setCheckboxVisible: (visible) => setCheckboxCssClasses((prev) => prev.setClass(\"ag-invisible\", !visible)),\n      setCheckboxSpacing: (add) => setCheckboxCssClasses((prev) => prev.setClass(\"ag-group-checkbox-spacing\", add))\n    };\n    const groupCellRendererCtrl = registry.createDynamicBean(\"groupCellRendererCtrl\", true);\n    if (groupCellRendererCtrl) {\n      ctrlRef.current = context.createBean(groupCellRendererCtrl);\n      ctrlRef.current.init(\n        compProxy,\n        eRef,\n        eCheckboxRef.current,\n        eExpandedRef.current,\n        eContractedRef.current,\n        GroupCellRenderer,\n        props\n      );\n    }\n  }, []);\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => `ag-cell-wrapper ${cssClasses.toString()}`, [cssClasses]);\n  const expandedClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => `ag-group-expanded ${expandedCssClasses.toString()}`, [expandedCssClasses]);\n  const contractedClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => `ag-group-contracted ${contractedCssClasses.toString()}`,\n    [contractedCssClasses]\n  );\n  const checkboxClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => `ag-group-checkbox ${checkboxCssClasses.toString()}`, [checkboxCssClasses]);\n  const useFwRenderer = innerCompDetails?.componentFromFramework;\n  const FwRenderer = useFwRenderer ? innerCompDetails.componentClass : void 0;\n  const useValue = innerCompDetails == null && value != null;\n  const escapedValue = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._toString)(value);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"span\",\n    {\n      className,\n      ref: setRef2,\n      ...!props.colDef ? { role: ctrlRef.current?.getCellAriaRole() } : {}\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: expandedClassName, ref: eExpandedRef }),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: contractedClassName, ref: eContractedRef }),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: checkboxClassName, ref: eCheckboxRef }),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: \"ag-group-value\", ref: eValueRef }, useValue ? escapedValue : useFwRenderer ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FwRenderer, { ...innerCompDetails.params }) : null),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: \"ag-group-child-count\" }, childCount)\n  );\n});\nvar groupCellRenderer_default = GroupCellRenderer;\n\n// packages/ag-grid-react/src/shared/customComp/customComponentWrapper.ts\n\n\n// packages/ag-grid-react/src/reactUi/customComp/customWrapperComp.tsx\n\n\n// packages/ag-grid-react/src/shared/customComp/customContext.ts\n\nvar CustomContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n  setMethods: () => {\n  }\n});\n\n// packages/ag-grid-react/src/reactUi/customComp/customWrapperComp.tsx\nvar CustomWrapperComp = (params) => {\n  const { initialProps, addUpdateCallback, CustomComponentClass, setMethods } = params;\n  const [{ key, ...props }, setProps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialProps);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    addUpdateCallback((newProps) => setProps(newProps));\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CustomContext.Provider, { value: { setMethods } }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CustomComponentClass, { key, ...props }));\n};\nvar customWrapperComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(CustomWrapperComp);\n\n// packages/ag-grid-react/src/shared/reactComponent.ts\n\n\n\n\n// packages/ag-grid-react/src/shared/keyGenerator.ts\nvar counter = 0;\nfunction generateNewKey() {\n  return `agPortalKey_${++counter}`;\n}\n\n// packages/ag-grid-react/src/shared/reactComponent.ts\nvar ReactComponent = class {\n  constructor(reactComponent, portalManager, componentType, suppressFallbackMethods) {\n    this.portal = null;\n    this.oldPortal = null;\n    this.reactComponent = reactComponent;\n    this.portalManager = portalManager;\n    this.componentType = componentType;\n    this.suppressFallbackMethods = !!suppressFallbackMethods;\n    this.statelessComponent = this.isStateless(this.reactComponent);\n    this.key = generateNewKey();\n    this.portalKey = generateNewKey();\n    this.instanceCreated = this.isStatelessComponent() ? ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise.resolve(false) : new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveInstanceCreated = resolve;\n    });\n  }\n  getGui() {\n    return this.eParentElement;\n  }\n  /** `getGui()` returns the parent element. This returns the actual root element. */\n  getRootElement() {\n    const firstChild = this.eParentElement.firstChild;\n    return firstChild;\n  }\n  destroy() {\n    if (this.componentInstance && typeof this.componentInstance.destroy == \"function\") {\n      this.componentInstance.destroy();\n    }\n    const portal = this.portal;\n    if (portal) {\n      this.portalManager.destroyPortal(portal);\n    }\n  }\n  createParentElement(params) {\n    const componentWrappingElement = this.portalManager.getComponentWrappingElement();\n    const eParentElement = document.createElement(componentWrappingElement || \"div\");\n    eParentElement.classList.add(\"ag-react-container\");\n    params.reactContainer = eParentElement;\n    return eParentElement;\n  }\n  statelessComponentRendered() {\n    return this.eParentElement.childElementCount > 0 || this.eParentElement.childNodes.length > 0;\n  }\n  getFrameworkComponentInstance() {\n    return this.componentInstance;\n  }\n  isStatelessComponent() {\n    return this.statelessComponent;\n  }\n  getReactComponentName() {\n    return this.reactComponent.name;\n  }\n  getMemoType() {\n    return this.hasSymbol() ? Symbol.for(\"react.memo\") : 60115;\n  }\n  hasSymbol() {\n    return typeof Symbol === \"function\" && Symbol.for;\n  }\n  isStateless(Component2) {\n    return typeof Component2 === \"function\" && !(Component2.prototype && Component2.prototype.isReactComponent) || typeof Component2 === \"object\" && Component2.$$typeof === this.getMemoType();\n  }\n  hasMethod(name) {\n    const frameworkComponentInstance = this.getFrameworkComponentInstance();\n    return !!frameworkComponentInstance && frameworkComponentInstance[name] != null || this.fallbackMethodAvailable(name);\n  }\n  callMethod(name, args) {\n    const frameworkComponentInstance = this.getFrameworkComponentInstance();\n    if (this.isStatelessComponent()) {\n      return this.fallbackMethod(name, !!args && args[0] ? args[0] : {});\n    } else if (!frameworkComponentInstance) {\n      setTimeout(() => this.callMethod(name, args));\n      return;\n    }\n    const method = frameworkComponentInstance[name];\n    if (method) {\n      return method.apply(frameworkComponentInstance, args);\n    }\n    if (this.fallbackMethodAvailable(name)) {\n      return this.fallbackMethod(name, !!args && args[0] ? args[0] : {});\n    }\n  }\n  addMethod(name, callback) {\n    this[name] = callback;\n  }\n  init(params) {\n    this.eParentElement = this.createParentElement(params);\n    this.createOrUpdatePortal(params);\n    return new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => this.createReactComponent(resolve));\n  }\n  createOrUpdatePortal(params) {\n    if (!this.isStatelessComponent()) {\n      this.ref = (element) => {\n        this.componentInstance = element;\n        this.resolveInstanceCreated?.(true);\n        this.resolveInstanceCreated = void 0;\n      };\n      params.ref = this.ref;\n    }\n    this.reactElement = this.createElement(this.reactComponent, { ...params, key: this.key });\n    this.portal = (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(\n      this.reactElement,\n      this.eParentElement,\n      this.portalKey\n      // fixed deltaRowModeRefreshCompRenderer\n    );\n  }\n  createElement(reactComponent, props) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(reactComponent, props);\n  }\n  createReactComponent(resolve) {\n    this.portalManager.mountReactPortal(this.portal, this, resolve);\n  }\n  rendered() {\n    return this.isStatelessComponent() && this.statelessComponentRendered() || !!(!this.isStatelessComponent() && this.getFrameworkComponentInstance());\n  }\n  /*\n   * fallback methods - these will be invoked if a corresponding instance method is not present\n   * for example if refresh is called and is not available on the component instance, then refreshComponent on this\n   * class will be invoked instead\n   *\n   * Currently only refresh is supported\n   */\n  refreshComponent(args) {\n    this.oldPortal = this.portal;\n    this.createOrUpdatePortal(args);\n    this.portalManager.updateReactPortal(this.oldPortal, this.portal);\n  }\n  fallbackMethod(name, params) {\n    const method = this[`${name}Component`];\n    if (!this.suppressFallbackMethods && !!method) {\n      return method.bind(this)(params);\n    }\n  }\n  fallbackMethodAvailable(name) {\n    if (this.suppressFallbackMethods) {\n      return false;\n    }\n    const method = this[`${name}Component`];\n    return !!method;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/customComponentWrapper.ts\nfunction addOptionalMethods(optionalMethodNames, providedMethods, component) {\n  optionalMethodNames.forEach((methodName) => {\n    const providedMethod = providedMethods[methodName];\n    if (providedMethod) {\n      component[methodName] = providedMethod;\n    }\n  });\n}\nvar CustomComponentWrapper = class extends ReactComponent {\n  constructor() {\n    super(...arguments);\n    this.awaitUpdateCallback = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveUpdateCallback = resolve;\n    });\n    this.wrapperComponent = customWrapperComp_default;\n  }\n  init(params) {\n    this.sourceParams = params;\n    return super.init(this.getProps());\n  }\n  addMethod() {\n  }\n  getInstance() {\n    return this.instanceCreated.then(() => this.componentInstance);\n  }\n  getFrameworkComponentInstance() {\n    return this;\n  }\n  createElement(reactComponent, props) {\n    return super.createElement(this.wrapperComponent, {\n      initialProps: props,\n      CustomComponentClass: reactComponent,\n      setMethods: (methods) => this.setMethods(methods),\n      addUpdateCallback: (callback) => {\n        this.updateCallback = () => {\n          callback(this.getProps());\n          return new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n            setTimeout(() => {\n              resolve();\n            });\n          });\n        };\n        this.resolveUpdateCallback();\n      }\n    });\n  }\n  setMethods(methods) {\n    this.providedMethods = methods;\n    addOptionalMethods(this.getOptionalMethods(), this.providedMethods, this);\n  }\n  getOptionalMethods() {\n    return [];\n  }\n  getProps() {\n    return {\n      ...this.sourceParams,\n      key: this.key,\n      ref: this.ref\n    };\n  }\n  refreshProps() {\n    if (this.updateCallback) {\n      return this.updateCallback();\n    }\n    return new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise(\n      (resolve) => this.awaitUpdateCallback.then(() => {\n        this.updateCallback().then(() => resolve());\n      })\n    );\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/cellRendererComponentWrapper.ts\nvar CellRendererComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/dateComponentWrapper.ts\nvar DateComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.date = null;\n    this.onDateChange = (date) => this.updateDate(date);\n  }\n  getDate() {\n    return this.date;\n  }\n  setDate(date) {\n    this.date = date;\n    this.refreshProps();\n  }\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\", \"setInputPlaceholder\", \"setInputAriaLabel\", \"setDisabled\"];\n  }\n  updateDate(date) {\n    this.setDate(date);\n    this.sourceParams.onDateChanged();\n  }\n  getProps() {\n    const props = super.getProps();\n    props.date = this.date;\n    props.onDateChange = this.onDateChange;\n    delete props.onDateChanged;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/dragAndDropImageComponentWrapper.ts\nvar DragAndDropImageComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.label = \"\";\n    this.icon = null;\n    this.shake = false;\n  }\n  setIcon(iconName, shake) {\n    this.icon = iconName;\n    this.shake = shake;\n    this.refreshProps();\n  }\n  setLabel(label) {\n    this.label = label;\n    this.refreshProps();\n  }\n  getProps() {\n    const props = super.getProps();\n    const { label, icon, shake } = this;\n    props.label = label;\n    props.icon = icon;\n    props.shake = shake;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/filterComponentWrapper.ts\n\nvar FilterComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.model = null;\n    this.onModelChange = (model) => this.updateModel(model);\n    this.onUiChange = () => this.sourceParams.filterModifiedCallback();\n    this.expectingNewMethods = true;\n    this.hasBeenActive = false;\n    this.awaitSetMethodsCallback = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveSetMethodsCallback = resolve;\n    });\n  }\n  isFilterActive() {\n    return this.model != null;\n  }\n  doesFilterPass(params) {\n    return this.providedMethods.doesFilterPass(params);\n  }\n  getModel() {\n    return this.model;\n  }\n  setModel(model) {\n    this.expectingNewMethods = true;\n    this.model = model;\n    this.hasBeenActive || (this.hasBeenActive = this.isFilterActive());\n    return this.refreshProps();\n  }\n  refresh(newParams) {\n    this.sourceParams = newParams;\n    this.refreshProps();\n    return true;\n  }\n  afterGuiAttached(params) {\n    const providedMethods = this.providedMethods;\n    if (!providedMethods) {\n      this.awaitSetMethodsCallback.then(() => this.providedMethods?.afterGuiAttached?.(params));\n    } else {\n      providedMethods.afterGuiAttached?.(params);\n    }\n  }\n  getOptionalMethods() {\n    return [\"afterGuiDetached\", \"onNewRowsLoaded\", \"getModelAsString\", \"onAnyFilterChanged\"];\n  }\n  setMethods(methods) {\n    if (this.expectingNewMethods === false && this.hasBeenActive && this.providedMethods?.doesFilterPass !== methods?.doesFilterPass) {\n      setTimeout(() => {\n        this.sourceParams.filterChangedCallback();\n      });\n    }\n    this.expectingNewMethods = false;\n    super.setMethods(methods);\n    this.resolveSetMethodsCallback();\n    this.resolveFilterPassCallback?.();\n    this.resolveFilterPassCallback = void 0;\n  }\n  updateModel(model) {\n    this.resolveFilterPassCallback?.();\n    const awaitFilterPassCallback = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveFilterPassCallback = resolve;\n    });\n    this.setModel(model).then(() => {\n      awaitFilterPassCallback.then(() => {\n        this.sourceParams.filterChangedCallback();\n      });\n    });\n  }\n  getProps() {\n    const props = super.getProps();\n    props.model = this.model;\n    props.onModelChange = this.onModelChange;\n    props.onUiChange = this.onUiChange;\n    delete props.filterChangedCallback;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/filterDisplayComponentWrapper.ts\n\nvar FilterDisplayComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.awaitSetMethodsCallback = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveSetMethodsCallback = resolve;\n    });\n  }\n  refresh(newParams) {\n    this.sourceParams = newParams;\n    this.refreshProps();\n    return true;\n  }\n  afterGuiAttached(params) {\n    const providedMethods = this.providedMethods;\n    if (!providedMethods) {\n      this.awaitSetMethodsCallback.then(() => this.providedMethods?.afterGuiAttached?.(params));\n    } else {\n      providedMethods.afterGuiAttached?.(params);\n    }\n  }\n  getOptionalMethods() {\n    return [\"afterGuiDetached\", \"onNewRowsLoaded\", \"onAnyFilterChanged\"];\n  }\n  setMethods(methods) {\n    super.setMethods(methods);\n    this.resolveSetMethodsCallback();\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/floatingFilterComponentProxy.ts\n\nfunction updateFloatingFilterParent(params, model) {\n  params.parentFilterInstance((instance) => {\n    (instance.setModel(model) || ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise.resolve()).then(() => {\n      params.filterParams.filterChangedCallback();\n    });\n  });\n}\nvar FloatingFilterComponentProxy = class {\n  constructor(floatingFilterParams, refreshProps) {\n    this.floatingFilterParams = floatingFilterParams;\n    this.refreshProps = refreshProps;\n    this.model = null;\n    this.onModelChange = (model) => this.updateModel(model);\n  }\n  getProps() {\n    return {\n      ...this.floatingFilterParams,\n      model: this.model,\n      onModelChange: this.onModelChange\n    };\n  }\n  onParentModelChanged(parentModel) {\n    this.model = parentModel;\n    this.refreshProps();\n  }\n  refresh(params) {\n    this.floatingFilterParams = params;\n    this.refreshProps();\n  }\n  setMethods(methods) {\n    addOptionalMethods(this.getOptionalMethods(), methods, this);\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\"];\n  }\n  updateModel(model) {\n    this.model = model;\n    this.refreshProps();\n    updateFloatingFilterParent(this.floatingFilterParams, model);\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/floatingFilterComponentWrapper.ts\nvar FloatingFilterComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.model = null;\n    this.onModelChange = (model) => this.updateModel(model);\n  }\n  onParentModelChanged(parentModel) {\n    this.model = parentModel;\n    this.refreshProps();\n  }\n  refresh(newParams) {\n    this.sourceParams = newParams;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\"];\n  }\n  updateModel(model) {\n    this.model = model;\n    this.refreshProps();\n    updateFloatingFilterParent(this.sourceParams, model);\n  }\n  getProps() {\n    const props = super.getProps();\n    props.model = this.model;\n    props.onModelChange = this.onModelChange;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/floatingFilterDisplayComponentWrapper.ts\nvar FloatingFilterDisplayComponentWrapper = class extends CustomComponentWrapper {\n  refresh(newParams) {\n    this.sourceParams = newParams;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\"];\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/innerHeaderComponentWrapper.ts\nvar InnerHeaderComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/loadingOverlayComponentWrapper.ts\nvar LoadingOverlayComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/menuItemComponentWrapper.ts\nvar MenuItemComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.active = false;\n    this.expanded = false;\n    this.onActiveChange = (active) => this.updateActive(active);\n  }\n  setActive(active) {\n    this.awaitSetActive(active);\n  }\n  setExpanded(expanded) {\n    this.expanded = expanded;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"select\", \"configureDefaults\"];\n  }\n  awaitSetActive(active) {\n    this.active = active;\n    return this.refreshProps();\n  }\n  updateActive(active) {\n    const result = this.awaitSetActive(active);\n    if (active) {\n      result.then(() => this.sourceParams.onItemActivated());\n    }\n  }\n  getProps() {\n    const props = super.getProps();\n    props.active = this.active;\n    props.expanded = this.expanded;\n    props.onActiveChange = this.onActiveChange;\n    delete props.onItemActivated;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/noRowsOverlayComponentWrapper.ts\nvar NoRowsOverlayComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/statusPanelComponentWrapper.ts\nvar StatusPanelComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/toolPanelComponentWrapper.ts\nvar ToolPanelComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.onStateChange = (state) => this.updateState(state);\n  }\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n  getState() {\n    return this.state;\n  }\n  updateState(state) {\n    this.state = state;\n    this.refreshProps();\n    this.sourceParams.onStateUpdated();\n  }\n  getProps() {\n    const props = super.getProps();\n    props.state = this.state;\n    props.onStateChange = this.onStateChange;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/util.ts\n\nfunction getInstance(wrapperComponent, callback) {\n  const promise = wrapperComponent?.getInstance?.() ?? ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise.resolve(void 0);\n  promise.then((comp) => callback(comp));\n}\nfunction warnReactiveCustomComponents() {\n  (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._warn)(231);\n}\n\n// packages/ag-grid-react/src/shared/portalManager.ts\nvar MAX_COMPONENT_CREATION_TIME_IN_MS = 1e3;\nvar PortalManager = class {\n  constructor(refresher, wrappingElement, maxComponentCreationTimeMs) {\n    this.destroyed = false;\n    this.portals = [];\n    this.hasPendingPortalUpdate = false;\n    this.wrappingElement = wrappingElement ? wrappingElement : \"div\";\n    this.refresher = refresher;\n    this.maxComponentCreationTimeMs = maxComponentCreationTimeMs ? maxComponentCreationTimeMs : MAX_COMPONENT_CREATION_TIME_IN_MS;\n  }\n  getPortals() {\n    return this.portals;\n  }\n  destroy() {\n    this.destroyed = true;\n  }\n  destroyPortal(portal) {\n    this.portals = this.portals.filter((curPortal) => curPortal !== portal);\n    this.batchUpdate();\n  }\n  getComponentWrappingElement() {\n    return this.wrappingElement;\n  }\n  mountReactPortal(portal, reactComponent, resolve) {\n    this.portals = [...this.portals, portal];\n    this.waitForInstance(reactComponent, resolve);\n    this.batchUpdate();\n  }\n  updateReactPortal(oldPortal, newPortal) {\n    this.portals[this.portals.indexOf(oldPortal)] = newPortal;\n    this.batchUpdate();\n  }\n  batchUpdate() {\n    if (this.hasPendingPortalUpdate) {\n      return;\n    }\n    setTimeout(() => {\n      if (!this.destroyed) {\n        this.refresher();\n        this.hasPendingPortalUpdate = false;\n      }\n    });\n    this.hasPendingPortalUpdate = true;\n  }\n  waitForInstance(reactComponent, resolve, startTime = Date.now()) {\n    if (this.destroyed) {\n      resolve(null);\n      return;\n    }\n    if (reactComponent.rendered()) {\n      resolve(reactComponent);\n    } else {\n      if (Date.now() - startTime >= this.maxComponentCreationTimeMs && !this.hasPendingPortalUpdate) {\n        agFlushSync(true, () => this.refresher());\n        if (reactComponent.rendered()) {\n          resolve(reactComponent);\n        }\n        return;\n      }\n      window.setTimeout(() => {\n        this.waitForInstance(reactComponent, resolve, startTime);\n      });\n    }\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/gridComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/gridBodyComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/gridHeaderComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/headerRowContainerComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/headerRowComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/headerCellComp.tsx\n\n\nvar HeaderCellComp = ({ ctrl }) => {\n  const isAlive = ctrl.isAlive();\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const colId = isAlive ? ctrl.column.getColId() : void 0;\n  const [userCompDetails, setUserCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eHeaderCompWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const userCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const cssManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (isAlive && !cssManager.current) {\n    cssManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.CssClassManager(() => eGui.current);\n  }\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef || !ctrl.isAlive()) {\n      return;\n    }\n    const refreshSelectAllGui = () => {\n      const selectAllGui = ctrl.getSelectAllGui();\n      if (selectAllGui) {\n        eResize.current?.insertAdjacentElement(\"afterend\", selectAllGui);\n        compBean.current.addDestroyFunc(() => selectAllGui.remove());\n      }\n    };\n    const compProxy = {\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      },\n      toggleCss: (name, on) => cssManager.current.toggleCss(name, on),\n      setUserStyles: (styles) => setUserStyles(styles),\n      setAriaSort: (sort) => {\n        if (eGui.current) {\n          sort ? (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._setAriaSort)(eGui.current, sort) : (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._removeAriaSort)(eGui.current);\n        }\n      },\n      setUserCompDetails: (compDetails) => setUserCompDetails(compDetails),\n      getUserCompInstance: () => userCompRef.current || void 0,\n      refreshSelectAllGui,\n      removeSelectAllGui: () => ctrl.getSelectAllGui()?.remove()\n    };\n    ctrl.setComp(compProxy, eRef, eResize.current, eHeaderCompWrapper.current, compBean.current);\n    refreshSelectAllGui();\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(\n    () => showJsComp(userCompDetails, context, eHeaderCompWrapper.current, userCompRef),\n    [userCompDetails]\n  );\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ctrl.setDragSource(eGui.current);\n  }, [userCompDetails]);\n  const userCompStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = userCompDetails?.componentFromFramework && isComponentStateless(userCompDetails.componentClass);\n    return !!res;\n  }, [userCompDetails]);\n  const reactUserComp = userCompDetails?.componentFromFramework;\n  const UserCompClass = userCompDetails?.componentClass;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, style: userStyles, className: \"ag-header-cell\", \"col-id\": colId, role: \"columnheader\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eResize, className: \"ag-header-cell-resize\", role: \"presentation\" }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eHeaderCompWrapper, className: \"ag-header-cell-comp-wrapper\", role: \"presentation\" }, reactUserComp ? userCompStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params, ref: userCompRef }) : null));\n};\nvar headerCellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderCellComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerFilterCellComp.tsx\n\n\n\n\n// packages/ag-grid-react/src/shared/customComp/floatingFilterDisplayComponentProxy.ts\nvar FloatingFilterDisplayComponentProxy = class {\n  constructor(floatingFilterParams, refreshProps) {\n    this.floatingFilterParams = floatingFilterParams;\n    this.refreshProps = refreshProps;\n  }\n  getProps() {\n    return this.floatingFilterParams;\n  }\n  refresh(params) {\n    this.floatingFilterParams = params;\n    this.refreshProps();\n  }\n  setMethods(methods) {\n    addOptionalMethods(this.getOptionalMethods(), methods, this);\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\"];\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/header/headerFilterCellComp.tsx\nvar HeaderFilterCellComp = ({ ctrl }) => {\n  const { context, gos } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => new CssClasses(\"ag-header-cell\", \"ag-floating-filter\")\n  );\n  const [cssBodyClasses, setBodyCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [cssButtonWrapperClasses, setButtonWrapperCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => new CssClasses(\"ag-floating-filter-button\", \"ag-hidden\")\n  );\n  const [buttonWrapperAriaHidden, setButtonWrapperAriaHidden] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"false\");\n  const [userCompDetails, setUserCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [, setRenderKey] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eFloatingFilterBody = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eButtonWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eButtonShowMainFilter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const userCompResolve = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const userCompPromise = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const userCompRef = (value) => {\n    if (value == null) {\n      return;\n    }\n    userCompResolve.current && userCompResolve.current(value);\n  };\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      return;\n    }\n    userCompPromise.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      userCompResolve.current = resolve;\n    });\n    const compProxy = {\n      toggleCss: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setUserStyles: (styles) => setUserStyles(styles),\n      addOrRemoveBodyCssClass: (name, on) => setBodyCssClasses((prev) => prev.setClass(name, on)),\n      setButtonWrapperDisplayed: (displayed) => {\n        setButtonWrapperCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed));\n        setButtonWrapperAriaHidden(!displayed ? \"true\" : \"false\");\n      },\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      },\n      setCompDetails: (compDetails) => setUserCompDetails(compDetails),\n      getFloatingFilterComp: () => userCompPromise.current ? userCompPromise.current : null,\n      setMenuIcon: (eIcon) => eButtonShowMainFilter.current?.appendChild(eIcon)\n    };\n    ctrl.setComp(compProxy, eRef, eButtonShowMainFilter.current, eFloatingFilterBody.current, compBean.current);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(\n    () => showJsComp(userCompDetails, context, eFloatingFilterBody.current, userCompRef),\n    [userCompDetails]\n  );\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssClasses.toString(), [cssClasses]);\n  const bodyClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssBodyClasses.toString(), [cssBodyClasses]);\n  const buttonWrapperClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssButtonWrapperClasses.toString(), [cssButtonWrapperClasses]);\n  const userCompStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = userCompDetails && userCompDetails.componentFromFramework && isComponentStateless(userCompDetails.componentClass);\n    return !!res;\n  }, [userCompDetails]);\n  const reactiveCustomComponents = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => gos.get(\"reactiveCustomComponents\"), []);\n  const enableFilterHandlers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => gos.get(\"enableFilterHandlers\"), []);\n  const floatingFilterCompProxy = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (userCompDetails?.componentFromFramework) {\n      if (reactiveCustomComponents) {\n        const ProxyClass = enableFilterHandlers ? FloatingFilterDisplayComponentProxy : FloatingFilterComponentProxy;\n        const compProxy = new ProxyClass(userCompDetails.params, () => setRenderKey((prev) => prev + 1));\n        userCompRef(compProxy);\n        floatingFilterCompProxy.current = compProxy;\n      } else {\n        warnReactiveCustomComponents();\n      }\n    }\n  }, [userCompDetails]);\n  const floatingFilterProps = floatingFilterCompProxy.current?.getProps();\n  const reactUserComp = userCompDetails?.componentFromFramework;\n  const UserCompClass = userCompDetails?.componentClass;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, style: userStyles, className, role: \"gridcell\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eFloatingFilterBody, className: bodyClassName, role: \"presentation\" }, reactUserComp ? reactiveCustomComponents ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    CustomContext.Provider,\n    {\n      value: {\n        setMethods: (methods) => floatingFilterCompProxy.current.setMethods(methods)\n      }\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...floatingFilterProps })\n  ) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params, ref: userCompStateless ? () => {\n  } : userCompRef }) : null), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: eButtonWrapper,\n      \"aria-hidden\": buttonWrapperAriaHidden,\n      className: buttonWrapperClassName,\n      role: \"presentation\"\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"button\",\n      {\n        ref: eButtonShowMainFilter,\n        type: \"button\",\n        className: \"ag-button ag-floating-filter-button-button\",\n        tabIndex: -1\n      }\n    )\n  ));\n};\nvar headerFilterCellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderFilterCellComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerGroupCellComp.tsx\n\n\nvar HeaderGroupCellComp = ({ ctrl }) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [cssResizableClasses, setResizableCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [resizableAriaHidden, setResizableAriaHidden] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"false\");\n  const [ariaExpanded, setAriaExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [userCompDetails, setUserCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const colId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ctrl.column.getUniqueId(), []);\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eHeaderCompWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const userCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      return;\n    }\n    const compProxy = {\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      },\n      toggleCss: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setUserStyles: (styles) => setUserStyles(styles),\n      setHeaderWrapperHidden: (hidden) => {\n        const headerCompWrapper = eHeaderCompWrapper.current;\n        if (!headerCompWrapper) {\n          return;\n        }\n        if (hidden) {\n          headerCompWrapper.style.setProperty(\"display\", \"none\");\n        } else {\n          headerCompWrapper.style.removeProperty(\"display\");\n        }\n      },\n      setHeaderWrapperMaxHeight: (value) => {\n        const headerCompWrapper = eHeaderCompWrapper.current;\n        if (!headerCompWrapper) {\n          return;\n        }\n        if (value != null) {\n          headerCompWrapper.style.setProperty(\"max-height\", `${value}px`);\n        } else {\n          headerCompWrapper.style.removeProperty(\"max-height\");\n        }\n        headerCompWrapper.classList.toggle(\"ag-header-cell-comp-wrapper-limited-height\", value != null);\n      },\n      setUserCompDetails: (compDetails) => setUserCompDetails(compDetails),\n      setResizableDisplayed: (displayed) => {\n        setResizableCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed));\n        setResizableAriaHidden(!displayed ? \"true\" : \"false\");\n      },\n      setAriaExpanded: (expanded) => setAriaExpanded(expanded),\n      getUserCompInstance: () => userCompRef.current || void 0\n    };\n    ctrl.setComp(compProxy, eRef, eResize.current, eHeaderCompWrapper.current, compBean.current);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => showJsComp(userCompDetails, context, eHeaderCompWrapper.current), [userCompDetails]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (eGui.current) {\n      ctrl.setDragSource(eGui.current);\n    }\n  }, [userCompDetails]);\n  const userCompStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = userCompDetails?.componentFromFramework && isComponentStateless(userCompDetails.componentClass);\n    return !!res;\n  }, [userCompDetails]);\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => \"ag-header-group-cell \" + cssClasses.toString(), [cssClasses]);\n  const resizableClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => \"ag-header-cell-resize \" + cssResizableClasses.toString(),\n    [cssResizableClasses]\n  );\n  const reactUserComp = userCompDetails?.componentFromFramework;\n  const UserCompClass = userCompDetails?.componentClass;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: setRef2,\n      style: userStyles,\n      className,\n      \"col-id\": colId,\n      role: \"columnheader\",\n      \"aria-expanded\": ariaExpanded\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eHeaderCompWrapper, className: \"ag-header-cell-comp-wrapper\", role: \"presentation\" }, reactUserComp ? userCompStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params, ref: userCompRef }) : null),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eResize, \"aria-hidden\": resizableAriaHidden, className: resizableClassName })\n  );\n};\nvar headerGroupCellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderGroupCellComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerRowComp.tsx\nvar HeaderRowComp = ({ ctrl }) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { topOffset, rowHeight } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ctrl.getTopAndHeight(), []);\n  const ariaRowIndex = ctrl.getAriaRowIndex();\n  const className = ctrl.headerRowClass;\n  const [height, setHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowHeight + \"px\");\n  const [top, setTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => topOffset + \"px\");\n  const cellCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const [cellCtrls, setCellCtrls] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => ctrl.getUpdatedHeaderCtrls());\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      return;\n    }\n    const compProxy = {\n      setHeight: (height2) => setHeight(height2),\n      setTop: (top2) => setTop(top2),\n      setHeaderCtrls: (ctrls, forceOrder, afterScroll) => {\n        const prevCellCtrls = cellCtrlsRef.current;\n        const nextCells = getNextValueIfDifferent(prevCellCtrls, ctrls, forceOrder);\n        if (nextCells !== prevCellCtrls) {\n          cellCtrlsRef.current = nextCells;\n          agFlushSync(afterScroll, () => setCellCtrls(nextCells));\n        }\n      },\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      }\n    };\n    ctrl.setComp(compProxy, compBean.current, false);\n  }, []);\n  const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height,\n      top\n    }),\n    [height, top]\n  );\n  const createCellJsx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((cellCtrl) => {\n    switch (ctrl.type) {\n      case \"group\":\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerGroupCellComp_default, { ctrl: cellCtrl, key: cellCtrl.instanceId });\n      case \"filter\":\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerFilterCellComp_default, { ctrl: cellCtrl, key: cellCtrl.instanceId });\n      default:\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerCellComp_default, { ctrl: cellCtrl, key: cellCtrl.instanceId });\n    }\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className, role: \"row\", style, \"aria-rowindex\": ariaRowIndex }, cellCtrls.map(createCellJsx));\n};\nvar headerRowComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderRowComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerRowContainerComp.tsx\nvar HeaderRowContainerComp = ({ pinned }) => {\n  const [displayed, setDisplayed] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n  const [headerRowCtrls, setHeaderRowCtrls] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eCenterContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const headerRowCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const pinnedLeft = pinned === \"left\";\n  const pinnedRight = pinned === \"right\";\n  const centre = !pinnedLeft && !pinnedRight;\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    headerRowCtrlRef.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.HeaderRowContainerCtrl(pinned)) : context.destroyBean(headerRowCtrlRef.current);\n    if (!eRef) {\n      return;\n    }\n    const compProxy = {\n      setDisplayed,\n      setCtrls: (ctrls) => setHeaderRowCtrls(ctrls),\n      // centre only\n      setCenterWidth: (width) => {\n        if (eCenterContainer.current) {\n          eCenterContainer.current.style.width = width;\n        }\n      },\n      setViewportScrollLeft: (left) => {\n        if (eGui.current) {\n          eGui.current.scrollLeft = left;\n        }\n      },\n      // pinned only\n      setPinnedContainerWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n          eGui.current.style.minWidth = width;\n          eGui.current.style.maxWidth = width;\n        }\n      }\n    };\n    headerRowCtrlRef.current.setComp(compProxy, eGui.current);\n  }, []);\n  const className = !displayed ? \"ag-hidden\" : \"\";\n  const insertRowsJsx = () => headerRowCtrls.map((ctrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowComp_default, { ctrl, key: ctrl.instanceId }));\n  return pinnedLeft ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: \"ag-pinned-left-header \" + className, \"aria-hidden\": !displayed, role: \"rowgroup\" }, insertRowsJsx()) : pinnedRight ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: \"ag-pinned-right-header \" + className, \"aria-hidden\": !displayed, role: \"rowgroup\" }, insertRowsJsx()) : centre ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: \"ag-header-viewport \" + className, role: \"presentation\", tabIndex: -1 }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eCenterContainer, className: \"ag-header-container\", role: \"rowgroup\" }, insertRowsJsx())) : null;\n};\nvar headerRowContainerComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderRowContainerComp);\n\n// packages/ag-grid-react/src/reactUi/header/gridHeaderComp.tsx\nvar GridHeaderComp = () => {\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [height, setHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const gridCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    gridCtrlRef.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.GridHeaderCtrl()) : context.destroyBean(gridCtrlRef.current);\n    if (!eRef)\n      return;\n    const compProxy = {\n      toggleCss: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setHeightAndMinHeight: (height2) => setHeight(height2)\n    };\n    gridCtrlRef.current.setComp(compProxy, eRef, eRef);\n  }, []);\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = cssClasses.toString();\n    return \"ag-header \" + res;\n  }, [cssClasses]);\n  const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height,\n      minHeight: height\n    }),\n    [height]\n  );\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className, style, role: \"presentation\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowContainerComp_default, { pinned: \"left\" }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowContainerComp_default, { pinned: null }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowContainerComp_default, { pinned: \"right\" }));\n};\nvar gridHeaderComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GridHeaderComp);\n\n// packages/ag-grid-react/src/reactUi/reactComment.tsx\n\nvar useReactCommentEffect = (comment, eForCommentRef) => {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const eForComment = eForCommentRef.current;\n    if (eForComment) {\n      const eParent = eForComment.parentElement;\n      if (eParent) {\n        const eComment = document.createComment(comment);\n        eParent.insertBefore(eComment, eForComment);\n        return () => {\n          eParent.removeChild(eComment);\n        };\n      }\n    }\n  }, [comment]);\n};\nvar reactComment_default = useReactCommentEffect;\n\n// packages/ag-grid-react/src/reactUi/rows/rowContainerComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/rows/rowComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/cells/cellComp.tsx\n\n\n\n// packages/ag-grid-react/src/shared/customComp/cellEditorComponentProxy.ts\n\nvar CellEditorComponentProxy = class {\n  constructor(cellEditorParams, refreshProps) {\n    this.cellEditorParams = cellEditorParams;\n    this.refreshProps = refreshProps;\n    this.instanceCreated = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveInstanceCreated = resolve;\n    });\n    this.onValueChange = (value) => this.updateValue(value);\n    this.value = cellEditorParams.value;\n  }\n  getProps() {\n    return {\n      ...this.cellEditorParams,\n      initialValue: this.cellEditorParams.value,\n      value: this.value,\n      onValueChange: this.onValueChange\n    };\n  }\n  getValue() {\n    return this.value;\n  }\n  refresh(params) {\n    this.cellEditorParams = params;\n    this.refreshProps();\n  }\n  setMethods(methods) {\n    addOptionalMethods(this.getOptionalMethods(), methods, this);\n  }\n  getInstance() {\n    return this.instanceCreated.then(() => this.componentInstance);\n  }\n  setRef(componentInstance) {\n    this.componentInstance = componentInstance;\n    this.resolveInstanceCreated?.();\n    this.resolveInstanceCreated = void 0;\n  }\n  getOptionalMethods() {\n    return [\n      \"isCancelBeforeStart\",\n      \"isCancelAfterEnd\",\n      \"focusIn\",\n      \"focusOut\",\n      \"afterGuiAttached\",\n      \"getValidationErrors\",\n      \"getValidationElement\"\n    ];\n  }\n  updateValue(value) {\n    this.value = value;\n    this.refreshProps();\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/cells/cellEditorComp.tsx\n\n\n// packages/ag-grid-react/src/reactUi/cells/popupEditorComp.tsx\n\n\n\n\n// packages/ag-grid-react/src/reactUi/useEffectOnce.tsx\n\nvar useEffectOnce = (effect) => {\n  const effectFn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(effect);\n  const destroyFn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const effectCalled = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const rendered = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const [, setVal] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  if (effectCalled.current) {\n    rendered.current = true;\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!effectCalled.current) {\n      destroyFn.current = effectFn.current();\n      effectCalled.current = true;\n    }\n    setVal((val) => val + 1);\n    return () => {\n      if (!rendered.current) {\n        return;\n      }\n      destroyFn.current?.();\n    };\n  }, []);\n};\n\n// packages/ag-grid-react/src/reactUi/cells/popupEditorComp.tsx\nvar PopupEditorComp = (props) => {\n  const [popupEditorWrapper, setPopupEditorWrapper] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { context, popupSvc, localeSvc, gos, editSvc } = beans;\n  const { editDetails, cellCtrl, eParentCell } = props;\n  useEffectOnce(() => {\n    const { compDetails } = editDetails;\n    const useModelPopup = gos.get(\"stopEditingWhenCellsLoseFocus\");\n    const wrapper = context.createBean(editSvc.createPopupEditorWrapper(compDetails.params));\n    const ePopupGui = wrapper.getGui();\n    if (props.jsChildComp) {\n      const eChildGui = props.jsChildComp.getGui();\n      if (eChildGui) {\n        ePopupGui.appendChild(eChildGui);\n      }\n    }\n    const { column, rowNode } = cellCtrl;\n    const positionParams = {\n      column,\n      rowNode,\n      type: \"popupCellEditor\",\n      eventSource: eParentCell,\n      ePopup: ePopupGui,\n      position: editDetails.popupPosition,\n      keepWithinBounds: true\n    };\n    const positionCallback = popupSvc?.positionPopupByComponent.bind(popupSvc, positionParams);\n    const translate = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getLocaleTextFunc)(localeSvc);\n    const addPopupRes = popupSvc?.addPopup({\n      modal: useModelPopup,\n      eChild: ePopupGui,\n      closeOnEsc: true,\n      closedCallback: () => {\n        cellCtrl.onPopupEditorClosed();\n      },\n      anchorToElement: eParentCell,\n      positionCallback,\n      ariaLabel: translate(\"ariaLabelCellEditor\", \"Cell Editor\")\n    });\n    const hideEditorPopup = addPopupRes ? addPopupRes.hideFunc : void 0;\n    setPopupEditorWrapper(wrapper);\n    props.jsChildComp?.afterGuiAttached?.();\n    return () => {\n      hideEditorPopup?.();\n      context.destroyBean(wrapper);\n    };\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    return () => {\n      if (cellCtrl.isCellFocused() && popupEditorWrapper?.getGui().contains((0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getActiveDomElement)(beans))) {\n        eParentCell.focus({ preventScroll: true });\n      }\n    };\n  }, [popupEditorWrapper]);\n  return popupEditorWrapper && props.wrappedContent ? (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(props.wrappedContent, popupEditorWrapper.getGui()) : null;\n};\nvar popupEditorComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(PopupEditorComp);\n\n// packages/ag-grid-react/src/reactUi/cells/cellEditorComp.tsx\nvar jsxEditorProxy = (editDetails, CellEditorClass, setRef2) => {\n  const { compProxy } = editDetails;\n  setRef2(compProxy);\n  const props = compProxy.getProps();\n  const isStateless = isComponentStateless(CellEditorClass);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    CustomContext.Provider,\n    {\n      value: {\n        setMethods: (methods) => compProxy.setMethods(methods)\n      }\n    },\n    isStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellEditorClass, { ...props }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellEditorClass, { ...props, ref: (ref) => compProxy.setRef(ref) })\n  );\n};\nvar jsxEditor = (editDetails, CellEditorClass, setRef2) => {\n  const newFormat = editDetails.compProxy;\n  return newFormat ? jsxEditorProxy(editDetails, CellEditorClass, setRef2) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellEditorClass, { ...editDetails.compDetails.params, ref: setRef2 });\n};\nvar jsxEditValue = (editDetails, setCellEditorRef, eGui, cellCtrl, jsEditorComp) => {\n  const compDetails = editDetails.compDetails;\n  const CellEditorClass = compDetails.componentClass;\n  const reactInlineEditor = compDetails.componentFromFramework && !editDetails.popup;\n  const reactPopupEditor = compDetails.componentFromFramework && editDetails.popup;\n  const jsPopupEditor = !compDetails.componentFromFramework && editDetails.popup;\n  return reactInlineEditor ? jsxEditor(editDetails, CellEditorClass, setCellEditorRef) : reactPopupEditor ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    popupEditorComp_default,\n    {\n      editDetails,\n      cellCtrl,\n      eParentCell: eGui,\n      wrappedContent: jsxEditor(editDetails, CellEditorClass, setCellEditorRef)\n    }\n  ) : jsPopupEditor && jsEditorComp ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(popupEditorComp_default, { editDetails, cellCtrl, eParentCell: eGui, jsChildComp: jsEditorComp }) : null;\n};\n\n// packages/ag-grid-react/src/reactUi/cells/showJsRenderer.tsx\n\nvar useJsCellRenderer = (showDetails, showTools, eCellValue, cellValueVersion, jsCellRendererRef, eGui) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const destroyCellRenderer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const comp = jsCellRendererRef.current;\n    if (!comp) {\n      return;\n    }\n    const compGui = comp.getGui();\n    if (compGui && compGui.parentElement) {\n      compGui.parentElement.removeChild(compGui);\n    }\n    context.destroyBean(comp);\n    jsCellRendererRef.current = void 0;\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const showValue = showDetails != null;\n    const jsCompDetails = showDetails?.compDetails && !showDetails.compDetails.componentFromFramework;\n    const waitingForToolsSetup = showTools && eCellValue == null;\n    const showComp = showValue && jsCompDetails && !waitingForToolsSetup;\n    if (!showComp) {\n      destroyCellRenderer();\n      return;\n    }\n    const compDetails = showDetails.compDetails;\n    if (jsCellRendererRef.current) {\n      const comp = jsCellRendererRef.current;\n      const attemptRefresh = comp.refresh != null && showDetails.force == false;\n      const refreshResult = attemptRefresh ? comp.refresh(compDetails.params) : false;\n      const refreshWorked = refreshResult === true || refreshResult === void 0;\n      if (refreshWorked) {\n        return;\n      }\n      destroyCellRenderer();\n    }\n    const promise = compDetails.newAgStackInstance();\n    promise.then((comp) => {\n      if (!comp) {\n        return;\n      }\n      const compGui = comp.getGui();\n      if (!compGui) {\n        return;\n      }\n      const parent = showTools ? eCellValue : eGui.current;\n      parent.appendChild(compGui);\n      jsCellRendererRef.current = comp;\n    });\n  }, [showDetails, showTools, cellValueVersion]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return destroyCellRenderer;\n  }, []);\n};\nvar showJsRenderer_default = useJsCellRenderer;\n\n// packages/ag-grid-react/src/reactUi/cells/skeletonCellComp.tsx\n\nvar SkeletonCellRenderer = ({\n  cellCtrl,\n  parent\n}) => {\n  const jsCellRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const renderDetails = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const { loadingComp } = cellCtrl.getDeferLoadingCellRenderer();\n    return loadingComp ? {\n      value: void 0,\n      compDetails: loadingComp,\n      force: false\n    } : void 0;\n  }, [cellCtrl]);\n  showJsRenderer_default(renderDetails, false, void 0, 1, jsCellRendererRef, parent);\n  if (renderDetails?.compDetails?.componentFromFramework) {\n    const CellRendererClass = renderDetails.compDetails.componentClass;\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellRendererClass, { ...renderDetails.compDetails.params });\n  }\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null);\n};\n\n// packages/ag-grid-react/src/reactUi/cells/cellComp.tsx\nvar CellComp = ({\n  cellCtrl,\n  printLayout,\n  editingCell\n}) => {\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { context } = beans;\n  const {\n    column: { colIdSanitised },\n    instanceId\n  } = cellCtrl;\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [renderDetails, setRenderDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => cellCtrl.isCellRenderer() ? void 0 : { compDetails: void 0, value: cellCtrl.getValueToDisplay(), force: false }\n  );\n  const [editDetails, setEditDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [renderKey, setRenderKey] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [includeSelection, setIncludeSelection] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [includeRowDrag, setIncludeRowDrag] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [includeDndSource, setIncludeDndSource] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [jsEditorComp, setJsEditorComp] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const forceWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cellCtrl.isForceWrapper(), [cellCtrl]);\n  const cellAriaRole = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cellCtrl.getCellAriaRole(), [cellCtrl]);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const cellRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const jsCellRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const cellEditorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eCellWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const cellWrapperDestroyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const eCellValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [cellValueVersion, setCellValueVersion] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const setCellValueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    eCellValue.current = ref;\n    setCellValueVersion((v) => v + 1);\n  }, []);\n  const showTools = renderDetails != null && (includeSelection || includeDndSource || includeRowDrag) && (editDetails == null || !!editDetails.popup);\n  const showCellWrapper = forceWrapper || showTools;\n  const setCellEditorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (cellEditor) => {\n      cellEditorRef.current = cellEditor;\n      if (cellEditor) {\n        const editingCancelledByUserComp = cellEditor.isCancelBeforeStart && cellEditor.isCancelBeforeStart();\n        setTimeout(() => {\n          if (editingCancelledByUserComp) {\n            cellCtrl.stopEditing(true);\n            cellCtrl.focusCell(true);\n          } else {\n            cellCtrl.cellEditorAttached();\n            cellCtrl.enableEditorTooltipFeature(cellEditor);\n          }\n        });\n      }\n    },\n    [cellCtrl]\n  );\n  const cssManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (!cssManager.current) {\n    cssManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.CssClassManager(() => eGui.current);\n  }\n  showJsRenderer_default(renderDetails, showCellWrapper, eCellValue.current, cellValueVersion, jsCellRendererRef, eGui);\n  const lastRenderDetails = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    const oldDetails = lastRenderDetails.current;\n    const newDetails = renderDetails;\n    lastRenderDetails.current = renderDetails;\n    if (oldDetails == null || oldDetails.compDetails == null || newDetails == null || newDetails.compDetails == null) {\n      return;\n    }\n    const oldCompDetails = oldDetails.compDetails;\n    const newCompDetails = newDetails.compDetails;\n    if (oldCompDetails.componentClass != newCompDetails.componentClass) {\n      return;\n    }\n    if (cellRendererRef.current?.refresh == null) {\n      return;\n    }\n    const result = cellRendererRef.current.refresh(newCompDetails.params);\n    if (result != true) {\n      setRenderKey((prev) => prev + 1);\n    }\n  }, [renderDetails]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    const doingJsEditor = editDetails && !editDetails.compDetails.componentFromFramework;\n    if (!doingJsEditor) {\n      return;\n    }\n    const compDetails = editDetails.compDetails;\n    const isPopup = editDetails.popup === true;\n    const cellEditorPromise = compDetails.newAgStackInstance();\n    cellEditorPromise.then((cellEditor) => {\n      if (!cellEditor) {\n        return;\n      }\n      const compGui = cellEditor.getGui();\n      setCellEditorRef(cellEditor);\n      if (!isPopup) {\n        const parentEl = (forceWrapper ? eCellWrapper : eGui).current;\n        parentEl?.appendChild(compGui);\n        cellEditor.afterGuiAttached && cellEditor.afterGuiAttached();\n      }\n      setJsEditorComp(cellEditor);\n    });\n    return () => {\n      cellEditorPromise.then((cellEditor) => {\n        const compGui = cellEditor.getGui();\n        cellCtrl.disableEditorTooltipFeature();\n        context.destroyBean(cellEditor);\n        setCellEditorRef(void 0);\n        setJsEditorComp(void 0);\n        compGui?.parentElement?.removeChild(compGui);\n      });\n    };\n  }, [editDetails]);\n  const setCellWrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (eRef) => {\n      eCellWrapper.current = eRef;\n      if (!eRef) {\n        cellWrapperDestroyFuncs.current.forEach((f) => f());\n        cellWrapperDestroyFuncs.current = [];\n        return;\n      }\n      const addComp = (comp) => {\n        if (comp) {\n          const eGui2 = comp.getGui();\n          eRef.insertAdjacentElement(\"afterbegin\", eGui2);\n          cellWrapperDestroyFuncs.current.push(() => {\n            context.destroyBean(comp);\n            (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._removeFromParent)(eGui2);\n          });\n        }\n        return comp;\n      };\n      if (includeSelection) {\n        const checkboxSelectionComp = cellCtrl.createSelectionCheckbox();\n        addComp(checkboxSelectionComp);\n      }\n      if (includeDndSource) {\n        addComp(cellCtrl.createDndSource());\n      }\n      if (includeRowDrag) {\n        addComp(cellCtrl.createRowDragComp());\n      }\n    },\n    [cellCtrl, context, includeDndSource, includeRowDrag, includeSelection]\n  );\n  const init = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const spanReady = !cellCtrl.isCellSpanning() || eWrapper.current;\n    const eRef = eGui.current;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef || !spanReady || !cellCtrl) {\n      return;\n    }\n    const compProxy = {\n      toggleCss: (name, on) => cssManager.current.toggleCss(name, on),\n      setUserStyles: (styles) => setUserStyles(styles),\n      getFocusableElement: () => eGui.current,\n      setIncludeSelection: (include) => setIncludeSelection(include),\n      setIncludeRowDrag: (include) => setIncludeRowDrag(include),\n      setIncludeDndSource: (include) => setIncludeDndSource(include),\n      getCellEditor: () => cellEditorRef.current || null,\n      getCellRenderer: () => cellRendererRef.current ?? jsCellRendererRef.current,\n      getParentOfValue: () => eCellValue.current ?? eCellWrapper.current ?? eGui.current,\n      setRenderDetails: (compDetails, value, force) => {\n        const setDetails = () => {\n          setRenderDetails((prev) => {\n            if (prev?.compDetails !== compDetails || prev?.value !== value || prev?.force !== force) {\n              return {\n                value,\n                compDetails,\n                force\n              };\n            } else {\n              return prev;\n            }\n          });\n        };\n        if (compDetails?.params?.deferRender && !cellCtrl.rowNode.group) {\n          const { loadingComp, onReady } = cellCtrl.getDeferLoadingCellRenderer();\n          if (loadingComp) {\n            setRenderDetails({\n              value: void 0,\n              compDetails: loadingComp,\n              force: false\n            });\n            onReady.then(() => agStartTransition(setDetails));\n            return;\n          }\n        }\n        setDetails();\n      },\n      setEditDetails: (compDetails, popup, popupPosition, reactiveCustomComponents) => {\n        if (compDetails) {\n          let compProxy2 = void 0;\n          if (reactiveCustomComponents) {\n            compProxy2 = new CellEditorComponentProxy(\n              compDetails.params,\n              () => setRenderKey((prev) => prev + 1)\n            );\n          } else if (compDetails.componentFromFramework) {\n            warnReactiveCustomComponents();\n          }\n          setEditDetails({\n            compDetails,\n            popup,\n            popupPosition,\n            compProxy: compProxy2\n          });\n          if (!popup) {\n            setRenderDetails(void 0);\n          }\n        } else {\n          const recoverFocus = cellCtrl.hasBrowserFocus();\n          if (recoverFocus) {\n            compProxy.getFocusableElement().focus({ preventScroll: true });\n          }\n          setEditDetails((editDetails2) => {\n            if (editDetails2?.compProxy) {\n              cellEditorRef.current = void 0;\n            }\n            return void 0;\n          });\n        }\n      },\n      refreshEditStyles: (editing, isPopup) => {\n        if (!eGui.current) {\n          return;\n        }\n        const { current } = cssManager;\n        current.toggleCss(\"ag-cell-value\", !showCellWrapper);\n        current.toggleCss(\"ag-cell-inline-editing\", !!editing && !isPopup);\n        current.toggleCss(\"ag-cell-popup-editing\", !!editing && !!isPopup);\n        current.toggleCss(\"ag-cell-not-inline-editing\", !editing || !!isPopup);\n      }\n    };\n    const cellWrapperOrUndefined = eCellWrapper.current || void 0;\n    cellCtrl.setComp(\n      compProxy,\n      eRef,\n      eWrapper.current ?? void 0,\n      cellWrapperOrUndefined,\n      printLayout,\n      editingCell,\n      compBean.current\n    );\n  }, []);\n  const setGuiRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    eGui.current = ref;\n    init();\n  }, []);\n  const setWrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    eWrapper.current = ref;\n    init();\n  }, []);\n  const reactCellRendererStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = renderDetails?.compDetails?.componentFromFramework && isComponentStateless(renderDetails.compDetails.componentClass);\n    return !!res;\n  }, [renderDetails]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (!eGui.current) {\n      return;\n    }\n    const { current } = cssManager;\n    current.toggleCss(\"ag-cell-value\", !showCellWrapper);\n    current.toggleCss(\"ag-cell-inline-editing\", !!editDetails && !editDetails.popup);\n    current.toggleCss(\"ag-cell-popup-editing\", !!editDetails && !!editDetails.popup);\n    current.toggleCss(\"ag-cell-not-inline-editing\", !editDetails || !!editDetails.popup);\n  });\n  const valueOrCellComp = () => {\n    const { compDetails, value } = renderDetails;\n    if (!compDetails) {\n      return value?.toString?.() ?? value;\n    }\n    if (compDetails.componentFromFramework) {\n      const CellRendererClass = compDetails.componentClass;\n      return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Suspense, { fallback: /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SkeletonCellRenderer, { cellCtrl, parent: eGui }) }, reactCellRendererStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellRendererClass, { ...compDetails.params, key: renderKey }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellRendererClass, { ...compDetails.params, key: renderKey, ref: cellRendererRef }));\n    }\n  };\n  const showCellOrEditor = () => {\n    const showCellValue = () => {\n      if (renderDetails == null) {\n        return null;\n      }\n      return showCellWrapper ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { role: \"presentation\", id: `cell-${instanceId}`, className: \"ag-cell-value\", ref: setCellValueRef }, valueOrCellComp()) : valueOrCellComp();\n    };\n    const showEditValue = (details) => jsxEditValue(details, setCellEditorRef, eGui.current, cellCtrl, jsEditorComp);\n    if (editDetails != null) {\n      if (editDetails.popup) {\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, showCellValue(), showEditValue(editDetails));\n      }\n      return showEditValue(editDetails);\n    }\n    return showCellValue();\n  };\n  const renderCell = () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setGuiRef, style: userStyles, role: cellAriaRole, \"col-id\": colIdSanitised }, showCellWrapper ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: \"ag-cell-wrapper\", role: \"presentation\", ref: setCellWrapperRef }, showCellOrEditor()) : showCellOrEditor());\n  if (cellCtrl.isCellSpanning()) {\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setWrapperRef, className: \"ag-spanned-cell-wrapper\", role: \"presentation\" }, renderCell());\n  }\n  return renderCell();\n};\nvar cellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(CellComp);\n\n// packages/ag-grid-react/src/reactUi/rows/rowComp.tsx\nvar RowComp = ({ rowCtrl, containerType }) => {\n  const { context, gos, editSvc } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const enableUses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(RenderModeContext) === \"default\";\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const domOrderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(rowCtrl.getDomOrder());\n  const isFullWidth = rowCtrl.isFullWidth();\n  const isDisplayed = rowCtrl.rowNode.displayed;\n  const [rowIndex, setRowIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => isDisplayed ? rowCtrl.rowNode.getRowIndexString() : null\n  );\n  const [rowId, setRowId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowCtrl.rowId);\n  const [rowBusinessKey, setRowBusinessKey] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowCtrl.businessKey);\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowCtrl.rowStyles);\n  const cellCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [cellCtrlsFlushSync, setCellCtrlsFlushSync] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => null);\n  const [fullWidthCompDetails, setFullWidthCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [top, setTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => isDisplayed ? rowCtrl.getInitialRowTop(containerType) : void 0\n  );\n  const [transform, setTransform] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => isDisplayed ? rowCtrl.getInitialTransform(containerType) : void 0\n  );\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const fullWidthCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const autoHeightSetup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const [autoHeightSetupAttempt, setAutoHeightSetupAttempt] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (autoHeightSetup.current || !fullWidthCompDetails || autoHeightSetupAttempt > 10) {\n      return;\n    }\n    const eChild = eGui.current?.firstChild;\n    if (eChild) {\n      rowCtrl.setupDetailRowAutoHeight(eChild);\n      autoHeightSetup.current = true;\n    } else {\n      setAutoHeightSetupAttempt((prev) => prev + 1);\n    }\n  }, [fullWidthCompDetails, autoHeightSetupAttempt]);\n  const cssManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (!cssManager.current) {\n    cssManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.CssClassManager(() => eGui.current);\n  }\n  const cellsChanged = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(() => {\n  });\n  const sub = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((onStoreChange) => {\n    cellsChanged.current = onStoreChange;\n    return () => {\n      cellsChanged.current = () => {\n      };\n    };\n  }, []);\n  const cellCtrlsUses = agUseSyncExternalStore(\n    sub,\n    () => {\n      return cellCtrlsRef.current;\n    },\n    []\n  );\n  const cellCtrlsMerged = enableUses ? cellCtrlsUses : cellCtrlsFlushSync;\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      rowCtrl.unsetComp(containerType);\n      return;\n    }\n    if (!rowCtrl.isAlive()) {\n      return;\n    }\n    const compProxy = {\n      // the rowTop is managed by state, instead of direct style manipulation by rowCtrl (like all the other styles)\n      // as we need to have an initial value when it's placed into he DOM for the first time, for animation to work.\n      setTop,\n      setTransform,\n      // i found using React for managing classes at the row level was to slow, as modifying classes caused a lot of\n      // React code to execute, so avoiding React for managing CSS Classes made the grid go much faster.\n      toggleCss: (name, on) => cssManager.current.toggleCss(name, on),\n      setDomOrder: (domOrder) => domOrderRef.current = domOrder,\n      setRowIndex,\n      setRowId,\n      setRowBusinessKey,\n      setUserStyles,\n      // if we don't maintain the order, then cols will be ripped out and into the dom\n      // when cols reordered, which would stop the CSS transitions from working\n      setCellCtrls: (next, useFlushSync) => {\n        const prevCellCtrls = cellCtrlsRef.current;\n        const nextCells = getNextValueIfDifferent(prevCellCtrls, next, domOrderRef.current);\n        if (nextCells !== prevCellCtrls) {\n          cellCtrlsRef.current = nextCells;\n          if (enableUses) {\n            cellsChanged.current();\n          } else {\n            agFlushSync(useFlushSync, () => setCellCtrlsFlushSync(nextCells));\n          }\n        }\n      },\n      showFullWidth: (compDetails) => setFullWidthCompDetails(compDetails),\n      getFullWidthCellRenderer: () => fullWidthCompRef.current,\n      refreshFullWidth: (getUpdatedParams) => {\n        if (canRefreshFullWidthRef.current) {\n          setFullWidthCompDetails((prevFullWidthCompDetails) => ({\n            ...prevFullWidthCompDetails,\n            params: getUpdatedParams()\n          }));\n          return true;\n        } else {\n          if (!fullWidthCompRef.current || !fullWidthCompRef.current.refresh) {\n            return false;\n          }\n          return fullWidthCompRef.current.refresh(getUpdatedParams());\n        }\n      }\n    };\n    rowCtrl.setComp(compProxy, eRef, containerType, compBean.current);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(\n    () => showJsComp(fullWidthCompDetails, context, eGui.current, fullWidthCompRef),\n    [fullWidthCompDetails]\n  );\n  const rowStyles = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = { top, transform };\n    Object.assign(res, userStyles);\n    return res;\n  }, [top, transform, userStyles]);\n  const showFullWidthFramework = isFullWidth && fullWidthCompDetails?.componentFromFramework;\n  const showCells = !isFullWidth && cellCtrlsMerged != null;\n  const reactFullWidthCellRendererStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = fullWidthCompDetails?.componentFromFramework && isComponentStateless(fullWidthCompDetails.componentClass);\n    return !!res;\n  }, [fullWidthCompDetails]);\n  const canRefreshFullWidthRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    canRefreshFullWidthRef.current = reactFullWidthCellRendererStateless && !!fullWidthCompDetails && !!gos.get(\"reactiveCustomComponents\");\n  }, [reactFullWidthCellRendererStateless, fullWidthCompDetails]);\n  const showCellsJsx = () => cellCtrlsMerged?.map((cellCtrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    cellComp_default,\n    {\n      cellCtrl,\n      editingCell: editSvc?.isEditing(cellCtrl, { withOpenEditor: true }) ?? false,\n      printLayout: rowCtrl.printLayout,\n      key: cellCtrl.instanceId\n    }\n  ));\n  const showFullWidthFrameworkJsx = () => {\n    const FullWidthComp = fullWidthCompDetails.componentClass;\n    return reactFullWidthCellRendererStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FullWidthComp, { ...fullWidthCompDetails.params }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FullWidthComp, { ...fullWidthCompDetails.params, ref: fullWidthCompRef });\n  };\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: setRef2,\n      role: \"row\",\n      style: rowStyles,\n      \"row-index\": rowIndex,\n      \"row-id\": rowId,\n      \"row-business-key\": rowBusinessKey\n    },\n    showCells ? showCellsJsx() : showFullWidthFramework ? showFullWidthFrameworkJsx() : null\n  );\n};\nvar rowComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(RowComp);\n\n// packages/ag-grid-react/src/reactUi/rows/rowContainerComp.tsx\nvar RowContainerComp = ({ name }) => {\n  const { context, gos } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const containerOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getRowContainerOptions)(name), [name]);\n  const eViewport = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eSpanContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const rowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const prevRowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const [rowCtrlsOrdered, setRowCtrlsOrdered] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => []);\n  const isSpanning = !!gos.get(\"enableCellSpan\") && !!containerOptions.getSpannedRowCtrls;\n  const spannedRowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const prevSpannedRowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const [spannedRowCtrlsOrdered, setSpannedRowCtrlsOrdered] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => []);\n  const domOrderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const rowContainerCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const viewportClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-viewport\", (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getRowViewportClass)(name)), [name]);\n  const containerClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList((0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getRowContainerClass)(name)), [name]);\n  const spanClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-spanning-container\", (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getRowSpanContainerClass)(name)), [name]);\n  const shouldRenderViewport = containerOptions.type === \"center\" || isSpanning;\n  const topLevelRef = shouldRenderViewport ? eViewport : eContainer;\n  reactComment_default(\" AG Row Container \" + name + \" \", topLevelRef);\n  const areElementsReady = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const viewportReady = !shouldRenderViewport || eViewport.current != null;\n    const containerReady = eContainer.current != null;\n    const spanContainerReady = !isSpanning || eSpanContainer.current != null;\n    return viewportReady && containerReady && spanContainerReady;\n  }, []);\n  const areElementsRemoved = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    return eViewport.current == null && eContainer.current == null && eSpanContainer.current == null;\n  }, []);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (areElementsRemoved()) {\n      rowContainerCtrlRef.current = context.destroyBean(rowContainerCtrlRef.current);\n    }\n    if (areElementsReady()) {\n      const updateRowCtrlsOrdered = (useFlushSync) => {\n        const next = getNextValueIfDifferent(\n          prevRowCtrlsRef.current,\n          rowCtrlsRef.current,\n          domOrderRef.current\n        );\n        if (next !== prevRowCtrlsRef.current) {\n          prevRowCtrlsRef.current = next;\n          agFlushSync(useFlushSync, () => setRowCtrlsOrdered(next));\n        }\n      };\n      const updateSpannedRowCtrlsOrdered = (useFlushSync) => {\n        const next = getNextValueIfDifferent(\n          prevSpannedRowCtrlsRef.current,\n          spannedRowCtrlsRef.current,\n          domOrderRef.current\n        );\n        if (next !== prevSpannedRowCtrlsRef.current) {\n          prevSpannedRowCtrlsRef.current = next;\n          agFlushSync(useFlushSync, () => setSpannedRowCtrlsOrdered(next));\n        }\n      };\n      const compProxy = {\n        setHorizontalScroll: (offset) => {\n          if (eViewport.current) {\n            eViewport.current.scrollLeft = offset;\n          }\n        },\n        setViewportHeight: (height) => {\n          if (eViewport.current) {\n            eViewport.current.style.height = height;\n          }\n        },\n        setRowCtrls: ({ rowCtrls, useFlushSync }) => {\n          const useFlush = !!useFlushSync && rowCtrlsRef.current.length > 0 && rowCtrls.length > 0;\n          rowCtrlsRef.current = rowCtrls;\n          updateRowCtrlsOrdered(useFlush);\n        },\n        setSpannedRowCtrls: (rowCtrls, useFlushSync) => {\n          const useFlush = !!useFlushSync && spannedRowCtrlsRef.current.length > 0 && rowCtrls.length > 0;\n          spannedRowCtrlsRef.current = rowCtrls;\n          updateSpannedRowCtrlsOrdered(useFlush);\n        },\n        setDomOrder: (domOrder) => {\n          if (domOrderRef.current != domOrder) {\n            domOrderRef.current = domOrder;\n            updateRowCtrlsOrdered(false);\n          }\n        },\n        setContainerWidth: (width) => {\n          if (eContainer.current) {\n            eContainer.current.style.width = width;\n          }\n        },\n        setOffsetTop: (offset) => {\n          if (eContainer.current) {\n            eContainer.current.style.transform = `translateY(${offset})`;\n          }\n        }\n      };\n      rowContainerCtrlRef.current = context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.RowContainerCtrl(name));\n      rowContainerCtrlRef.current.setComp(\n        compProxy,\n        eContainer.current,\n        eSpanContainer.current ?? void 0,\n        eViewport.current\n      );\n    }\n  }, [areElementsReady, areElementsRemoved]);\n  const setContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      eContainer.current = e;\n      setRef2();\n    },\n    [setRef2]\n  );\n  const setSpanContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      eSpanContainer.current = e;\n      setRef2();\n    },\n    [setRef2]\n  );\n  const setViewportRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      eViewport.current = e;\n      setRef2();\n    },\n    [setRef2]\n  );\n  const buildContainer = () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: containerClasses, ref: setContainerRef, role: \"rowgroup\" }, rowCtrlsOrdered.map((rowCtrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(rowComp_default, { rowCtrl, containerType: containerOptions.type, key: rowCtrl.instanceId })));\n  if (!shouldRenderViewport) {\n    return buildContainer();\n  }\n  const buildSpanContainer = () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: spanClasses, ref: setSpanContainerRef, role: \"rowgroup\" }, spannedRowCtrlsOrdered.map((rowCtrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(rowComp_default, { rowCtrl, containerType: containerOptions.type, key: rowCtrl.instanceId })));\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: viewportClasses, ref: setViewportRef, role: \"presentation\" }, buildContainer(), isSpanning ? buildSpanContainer() : null);\n};\nvar rowContainerComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(RowContainerComp);\n\n// packages/ag-grid-react/src/reactUi/gridBodyComp.tsx\nvar GridBodyComp = () => {\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { context, overlays } = beans;\n  const [rowAnimationClass, setRowAnimationClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [topHeight, setTopHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [bottomHeight, setBottomHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [stickyTopHeight, setStickyTopHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyTopTop, setStickyTopTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyTopWidth, setStickyTopWidth] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"100%\");\n  const [stickyBottomHeight, setStickyBottomHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyBottomBottom, setStickyBottomBottom] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyBottomWidth, setStickyBottomWidth] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"100%\");\n  const [topInvisible, setTopInvisible] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n  const [bottomInvisible, setBottomInvisible] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n  const [forceVerticalScrollClass, setForceVerticalScrollClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [topAndBottomOverflowY, setTopAndBottomOverflowY] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [cellSelectableCss, setCellSelectableCss] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [layoutClass, setLayoutClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"ag-layout-normal\");\n  const cssManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (!cssManager.current) {\n    cssManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.CssClassManager(() => eRoot.current);\n  }\n  const eRoot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eTop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eStickyTop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eStickyBottom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eBody = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eBodyViewport = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eBottom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const beansToDestroy = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const destroyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  reactComment_default(\" AG Grid Body \", eRoot);\n  reactComment_default(\" AG Pinned Top \", eTop);\n  reactComment_default(\" AG Sticky Top \", eStickyTop);\n  reactComment_default(\" AG Middle \", eBodyViewport);\n  reactComment_default(\" AG Pinned Bottom \", eBottom);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eRoot.current = eRef;\n    if (!eRef) {\n      beansToDestroy.current = context.destroyBeans(beansToDestroy.current);\n      destroyFuncs.current.forEach((f) => f());\n      destroyFuncs.current = [];\n      return;\n    }\n    if (!context) {\n      return;\n    }\n    const attachToDom = (eParent, eChild) => {\n      eParent.appendChild(eChild);\n      destroyFuncs.current.push(() => eParent.removeChild(eChild));\n    };\n    const newComp = (compClass) => {\n      const comp = context.createBean(new compClass());\n      beansToDestroy.current.push(comp);\n      return comp;\n    };\n    const addComp = (eParent, compClass, comment) => {\n      attachToDom(eParent, document.createComment(comment));\n      attachToDom(eParent, newComp(compClass).getGui());\n    };\n    addComp(eRef, ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.FakeHScrollComp, \" AG Fake Horizontal Scroll \");\n    const overlayComp = overlays?.getOverlayWrapperCompClass();\n    if (overlayComp) {\n      addComp(eRef, overlayComp, \" AG Overlay Wrapper \");\n    }\n    if (eBody.current) {\n      addComp(eBody.current, ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.FakeVScrollComp, \" AG Fake Vertical Scroll \");\n    }\n    const compProxy = {\n      setRowAnimationCssOnBodyViewport: setRowAnimationClass,\n      setColumnCount: (count) => {\n        if (eRoot.current) {\n          (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._setAriaColCount)(eRoot.current, count);\n        }\n      },\n      setRowCount: (count) => {\n        if (eRoot.current) {\n          (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._setAriaRowCount)(eRoot.current, count);\n        }\n      },\n      setTopHeight,\n      setBottomHeight,\n      setStickyTopHeight,\n      setStickyTopTop,\n      setStickyTopWidth,\n      setTopInvisible,\n      setBottomInvisible,\n      setColumnMovingCss: (cssClass, flag) => cssManager.current.toggleCss(cssClass, flag),\n      updateLayoutClasses: setLayoutClass,\n      setAlwaysVerticalScrollClass: setForceVerticalScrollClass,\n      setPinnedTopBottomOverflowY: setTopAndBottomOverflowY,\n      setCellSelectableCss: (cssClass, flag) => setCellSelectableCss(flag ? cssClass : null),\n      setBodyViewportWidth: (width) => {\n        if (eBodyViewport.current) {\n          eBodyViewport.current.style.width = width;\n        }\n      },\n      registerBodyViewportResizeListener: (listener) => {\n        if (eBodyViewport.current) {\n          const unsubscribeFromResize = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._observeResize)(beans, eBodyViewport.current, listener);\n          destroyFuncs.current.push(() => unsubscribeFromResize());\n        }\n      },\n      setStickyBottomHeight,\n      setStickyBottomBottom,\n      setStickyBottomWidth,\n      setGridRootRole: (role) => eRef.setAttribute(\"role\", role)\n    };\n    const ctrl = context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.GridBodyCtrl());\n    beansToDestroy.current.push(ctrl);\n    ctrl.setComp(\n      compProxy,\n      eRef,\n      eBodyViewport.current,\n      eTop.current,\n      eBottom.current,\n      eStickyTop.current,\n      eStickyBottom.current\n    );\n  }, []);\n  const rootClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-root\", \"ag-unselectable\", layoutClass), [layoutClass]);\n  const bodyViewportClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\n      \"ag-body-viewport\",\n      rowAnimationClass,\n      layoutClass,\n      forceVerticalScrollClass,\n      cellSelectableCss\n    ),\n    [rowAnimationClass, layoutClass, forceVerticalScrollClass, cellSelectableCss]\n  );\n  const bodyClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-body\", layoutClass), [layoutClass]);\n  const topClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-floating-top\", topInvisible ? \"ag-invisible\" : null, cellSelectableCss),\n    [cellSelectableCss, topInvisible]\n  );\n  const stickyTopClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-sticky-top\", cellSelectableCss), [cellSelectableCss]);\n  const stickyBottomClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-sticky-bottom\", stickyBottomHeight === \"0px\" ? \"ag-invisible\" : null, cellSelectableCss),\n    [cellSelectableCss, stickyBottomHeight]\n  );\n  const bottomClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-floating-bottom\", bottomInvisible ? \"ag-invisible\" : null, cellSelectableCss),\n    [cellSelectableCss, bottomInvisible]\n  );\n  const topStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: topHeight,\n      minHeight: topHeight,\n      overflowY: topAndBottomOverflowY\n    }),\n    [topHeight, topAndBottomOverflowY]\n  );\n  const stickyTopStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: stickyTopHeight,\n      top: stickyTopTop,\n      width: stickyTopWidth\n    }),\n    [stickyTopHeight, stickyTopTop, stickyTopWidth]\n  );\n  const stickyBottomStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: stickyBottomHeight,\n      bottom: stickyBottomBottom,\n      width: stickyBottomWidth\n    }),\n    [stickyBottomHeight, stickyBottomBottom, stickyBottomWidth]\n  );\n  const bottomStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: bottomHeight,\n      minHeight: bottomHeight,\n      overflowY: topAndBottomOverflowY\n    }),\n    [bottomHeight, topAndBottomOverflowY]\n  );\n  const createRowContainer = (container) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(rowContainerComp_default, { name: container, key: `${container}-container` });\n  const createSection = ({\n    section,\n    children,\n    className,\n    style\n  }) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: section, className, role: \"presentation\", style }, children.map(createRowContainer));\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: rootClasses }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(gridHeaderComp_default, null), createSection({\n    section: eTop,\n    className: topClasses,\n    style: topStyle,\n    children: [\"topLeft\", \"topCenter\", \"topRight\", \"topFullWidth\"]\n  }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: bodyClasses, ref: eBody, role: \"presentation\" }, createSection({\n    section: eBodyViewport,\n    className: bodyViewportClasses,\n    children: [\"left\", \"center\", \"right\", \"fullWidth\"]\n  })), createSection({\n    section: eStickyTop,\n    className: stickyTopClasses,\n    style: stickyTopStyle,\n    children: [\"stickyTopLeft\", \"stickyTopCenter\", \"stickyTopRight\", \"stickyTopFullWidth\"]\n  }), createSection({\n    section: eStickyBottom,\n    className: stickyBottomClasses,\n    style: stickyBottomStyle,\n    children: [\"stickyBottomLeft\", \"stickyBottomCenter\", \"stickyBottomRight\", \"stickyBottomFullWidth\"]\n  }), createSection({\n    section: eBottom,\n    className: bottomClasses,\n    style: bottomStyle,\n    children: [\"bottomLeft\", \"bottomCenter\", \"bottomRight\", \"bottomFullWidth\"]\n  }));\n};\nvar gridBodyComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GridBodyComp);\n\n// packages/ag-grid-react/src/reactUi/tabGuardComp.tsx\n\n\nvar TabGuardCompRef = (props, forwardRef4) => {\n  const { children, eFocusableElement, onTabKeyDown, gridCtrl, forceFocusOutWhenTabGuardsAreEmpty, isEmpty } = props;\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const topTabGuardRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const bottomTabGuardRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const tabGuardCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const setTabIndex = (value) => {\n    const processedValue = value == null ? void 0 : parseInt(value, 10).toString();\n    [topTabGuardRef, bottomTabGuardRef].forEach((tabGuard) => {\n      if (processedValue === void 0) {\n        tabGuard.current?.removeAttribute(\"tabindex\");\n      } else {\n        tabGuard.current?.setAttribute(\"tabindex\", processedValue);\n      }\n    });\n  };\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(forwardRef4, () => ({\n    forceFocusOutOfContainer(up) {\n      tabGuardCtrlRef.current?.forceFocusOutOfContainer(up);\n    }\n  }));\n  const setupCtrl = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const topTabGuard = topTabGuardRef.current;\n    const bottomTabGuard = bottomTabGuardRef.current;\n    if (!topTabGuard && !bottomTabGuard) {\n      tabGuardCtrlRef.current = context.destroyBean(tabGuardCtrlRef.current);\n      return;\n    }\n    if (topTabGuard && bottomTabGuard) {\n      const compProxy = {\n        setTabIndex\n      };\n      tabGuardCtrlRef.current = context.createBean(\n        new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.TabGuardCtrl({\n          comp: compProxy,\n          eTopGuard: topTabGuard,\n          eBottomGuard: bottomTabGuard,\n          eFocusableElement,\n          onTabKeyDown,\n          forceFocusOutWhenTabGuardsAreEmpty,\n          focusInnerElement: (fromBottom) => gridCtrl.focusInnerElement(fromBottom),\n          isEmpty\n        })\n      );\n    }\n  }, []);\n  const setTopRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      topTabGuardRef.current = e;\n      setupCtrl();\n    },\n    [setupCtrl]\n  );\n  const setBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      bottomTabGuardRef.current = e;\n      setupCtrl();\n    },\n    [setupCtrl]\n  );\n  const createTabGuard = (side) => {\n    const className = side === \"top\" ? ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.TabGuardClassNames.TAB_GUARD_TOP : ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.TabGuardClassNames.TAB_GUARD_BOTTOM;\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"div\",\n      {\n        className: `${ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.TabGuardClassNames.TAB_GUARD} ${className}`,\n        role: \"presentation\",\n        ref: side === \"top\" ? setTopRef : setBottomRef\n      }\n    );\n  };\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, createTabGuard(\"top\"), children, createTabGuard(\"bottom\"));\n};\nvar TabGuardComp = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(TabGuardCompRef);\nvar tabGuardComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(TabGuardComp);\n\n// packages/ag-grid-react/src/reactUi/gridComp.tsx\nvar GridComp = ({ context }) => {\n  const [rtlClass, setRtlClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [layoutClass, setLayoutClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [cursor, setCursor] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [userSelect, setUserSelect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [initialised, setInitialised] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [tabGuardReady, setTabGuardReady] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const gridCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eRootWrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const tabGuardRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [eGridBodyParent, setGridBodyParent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const focusInnerElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(() => void 0);\n  const paginationCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const focusableContainersRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const onTabKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => void 0, []);\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (context.isDestroyed()) {\n      return null;\n    }\n    return context.getBeans();\n  }, [context]);\n  reactComment_default(\" AG Grid \", eRootWrapperRef);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eRootWrapperRef.current = eRef;\n    gridCtrlRef.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.GridCtrl()) : context.destroyBean(gridCtrlRef.current);\n    if (!eRef || context.isDestroyed()) {\n      return;\n    }\n    const gridCtrl = gridCtrlRef.current;\n    focusInnerElementRef.current = gridCtrl.focusInnerElement.bind(gridCtrl);\n    const compProxy = {\n      destroyGridUi: () => {\n      },\n      // do nothing, as framework users destroy grid by removing the comp\n      setRtlClass,\n      forceFocusOutOfContainer: (up) => {\n        if (!up && paginationCompRef.current?.isDisplayed()) {\n          paginationCompRef.current.forceFocusOutOfContainer(up);\n          return;\n        }\n        tabGuardRef.current?.forceFocusOutOfContainer(up);\n      },\n      updateLayoutClasses: setLayoutClass,\n      getFocusableContainers: () => {\n        const comps = [];\n        const gridBodyCompEl = eRootWrapperRef.current?.querySelector(\".ag-root\");\n        if (gridBodyCompEl) {\n          comps.push({ getGui: () => gridBodyCompEl });\n        }\n        focusableContainersRef.current.forEach((comp) => {\n          if (comp.isDisplayed()) {\n            comps.push(comp);\n          }\n        });\n        return comps;\n      },\n      setCursor,\n      setUserSelect\n    };\n    gridCtrl.setComp(compProxy, eRef, eRef);\n    setInitialised(true);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const gridCtrl = gridCtrlRef.current;\n    const eRootWrapper = eRootWrapperRef.current;\n    if (!tabGuardReady || !beans || !gridCtrl || !eGridBodyParent || !eRootWrapper) {\n      return;\n    }\n    const beansToDestroy = [];\n    const {\n      watermarkSelector,\n      paginationSelector,\n      sideBarSelector,\n      statusBarSelector,\n      gridHeaderDropZonesSelector\n    } = gridCtrl.getOptionalSelectors();\n    const additionalEls = [];\n    if (gridHeaderDropZonesSelector) {\n      const headerDropZonesComp = context.createBean(new gridHeaderDropZonesSelector.component());\n      const eGui = headerDropZonesComp.getGui();\n      eRootWrapper.insertAdjacentElement(\"afterbegin\", eGui);\n      additionalEls.push(eGui);\n      beansToDestroy.push(headerDropZonesComp);\n    }\n    if (sideBarSelector) {\n      const sideBarComp = context.createBean(new sideBarSelector.component());\n      const eGui = sideBarComp.getGui();\n      const bottomTabGuard = eGridBodyParent.querySelector(\".ag-tab-guard-bottom\");\n      if (bottomTabGuard) {\n        bottomTabGuard.insertAdjacentElement(\"beforebegin\", eGui);\n        additionalEls.push(eGui);\n      }\n      beansToDestroy.push(sideBarComp);\n      focusableContainersRef.current.push(sideBarComp);\n    }\n    const addComponentToDom = (component) => {\n      const comp = context.createBean(new component());\n      const eGui = comp.getGui();\n      eRootWrapper.insertAdjacentElement(\"beforeend\", eGui);\n      additionalEls.push(eGui);\n      beansToDestroy.push(comp);\n      return comp;\n    };\n    if (statusBarSelector) {\n      addComponentToDom(statusBarSelector.component);\n    }\n    if (paginationSelector) {\n      const paginationComp = addComponentToDom(paginationSelector.component);\n      paginationCompRef.current = paginationComp;\n      focusableContainersRef.current.push(paginationComp);\n    }\n    if (watermarkSelector) {\n      addComponentToDom(watermarkSelector.component);\n    }\n    return () => {\n      context.destroyBeans(beansToDestroy);\n      additionalEls.forEach((el) => {\n        el.parentElement?.removeChild(el);\n      });\n    };\n  }, [tabGuardReady, eGridBodyParent, beans]);\n  const rootWrapperClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-root-wrapper\", rtlClass, layoutClass),\n    [rtlClass, layoutClass]\n  );\n  const rootWrapperBodyClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-root-wrapper-body\", \"ag-focus-managed\", layoutClass),\n    [layoutClass]\n  );\n  const topStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      userSelect: userSelect != null ? userSelect : \"\",\n      WebkitUserSelect: userSelect != null ? userSelect : \"\",\n      cursor: cursor != null ? cursor : \"\"\n    }),\n    [userSelect, cursor]\n  );\n  const setTabGuardCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    tabGuardRef.current = ref;\n    setTabGuardReady(ref !== null);\n  }, []);\n  const isFocusable = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => !gridCtrlRef.current?.isFocusable(), []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: rootWrapperClasses, style: topStyle, role: \"presentation\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: rootWrapperBodyClasses, ref: setGridBodyParent, role: \"presentation\" }, initialised && eGridBodyParent && beans && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(BeansContext.Provider, { value: beans }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    tabGuardComp_default,\n    {\n      ref: setTabGuardCompRef,\n      eFocusableElement: eGridBodyParent,\n      onTabKeyDown,\n      gridCtrl: gridCtrlRef.current,\n      forceFocusOutWhenTabGuardsAreEmpty: true,\n      isEmpty: isFocusable\n    },\n    // we wait for initialised before rending the children, so GridComp has created and registered with it's\n    // GridCtrl before we create the child GridBodyComp. Otherwise the GridBodyComp would initialise first,\n    // before we have set the the Layout CSS classes, causing the GridBodyComp to render rows to a grid that\n    // doesn't have it's height specified, which would result if all the rows getting rendered (and if many rows,\n    // hangs the UI)\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(gridBodyComp_default, null)\n  ))));\n};\nvar gridComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GridComp);\n\n// packages/ag-grid-react/src/reactUi/renderStatusService.tsx\n\nvar RenderStatusService = class extends ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.BeanStub {\n  wireBeans(beans) {\n    this.ctrlsSvc = beans.ctrlsSvc;\n  }\n  areHeaderCellsRendered() {\n    return this.ctrlsSvc.getHeaderRowContainerCtrls().every((container) => container.getAllCtrls().every((ctrl) => ctrl.areCellsRendered()));\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/agGridReactUi.tsx\nvar deprecatedProps = {\n  setGridApi: void 0,\n  maxComponentCreationTimeMs: void 0,\n  children: void 0\n};\nvar reactPropsNotGridOptions = {\n  gridOptions: void 0,\n  modules: void 0,\n  containerStyle: void 0,\n  className: void 0,\n  passGridApi: void 0,\n  componentWrappingElement: void 0,\n  ...deprecatedProps\n};\nvar excludeReactCompProps = new Set(Object.keys(reactPropsNotGridOptions));\nvar deprecatedReactCompProps = new Set(Object.keys(deprecatedProps));\nvar AgGridReactUi = (props) => {\n  const apiRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const portalManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const destroyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const whenReadyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const prevProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(props);\n  const frameworkOverridesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const gridIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const ready = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const [context, setContext] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const [, setPortalRefresher] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    if (!eRef) {\n      destroyFuncs.current.forEach((f) => f());\n      destroyFuncs.current.length = 0;\n      return;\n    }\n    const modules = props.modules || [];\n    if (!portalManager.current) {\n      portalManager.current = new PortalManager(\n        () => setPortalRefresher((prev) => prev + 1),\n        props.componentWrappingElement,\n        props.maxComponentCreationTimeMs\n      );\n      destroyFuncs.current.push(() => {\n        portalManager.current?.destroy();\n        portalManager.current = null;\n      });\n    }\n    const mergedGridOps = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._combineAttributesAndGridOptions)(\n      props.gridOptions,\n      props,\n      Object.keys(props).filter((key) => !excludeReactCompProps.has(key))\n    );\n    const processQueuedUpdates = () => {\n      if (ready.current) {\n        const getFn = () => frameworkOverridesRef.current?.shouldQueueUpdates() ? void 0 : whenReadyFuncs.current.shift();\n        let fn = getFn();\n        while (fn) {\n          fn();\n          fn = getFn();\n        }\n      }\n    };\n    const frameworkOverrides = new ReactFrameworkOverrides(processQueuedUpdates);\n    frameworkOverridesRef.current = frameworkOverrides;\n    const renderStatus = new RenderStatusService();\n    const gridParams = {\n      providedBeanInstances: {\n        frameworkCompWrapper: new ReactFrameworkComponentWrapper(portalManager.current, mergedGridOps),\n        renderStatus\n      },\n      modules,\n      frameworkOverrides,\n      setThemeOnGridDiv: true\n    };\n    const createUiCallback = (context2) => {\n      setContext(context2);\n      context2.createBean(renderStatus);\n      destroyFuncs.current.push(() => {\n        context2.destroy();\n      });\n      context2.getBean(\"ctrlsSvc\").whenReady(\n        {\n          addDestroyFunc: (func) => {\n            destroyFuncs.current.push(func);\n          }\n        },\n        () => {\n          if (context2.isDestroyed()) {\n            return;\n          }\n          const api = apiRef.current;\n          if (api) {\n            props.passGridApi?.(api);\n          }\n        }\n      );\n    };\n    const acceptChangesCallback = (context2) => {\n      context2.getBean(\"ctrlsSvc\").whenReady(\n        {\n          addDestroyFunc: (func) => {\n            destroyFuncs.current.push(func);\n          }\n        },\n        () => {\n          whenReadyFuncs.current.forEach((f) => f());\n          whenReadyFuncs.current.length = 0;\n          ready.current = true;\n        }\n      );\n    };\n    const gridCoreCreator = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.GridCoreCreator();\n    mergedGridOps.gridId ?? (mergedGridOps.gridId = gridIdRef.current);\n    apiRef.current = gridCoreCreator.create(\n      eRef,\n      mergedGridOps,\n      createUiCallback,\n      acceptChangesCallback,\n      gridParams\n    );\n    destroyFuncs.current.push(() => {\n      apiRef.current = void 0;\n    });\n    if (apiRef.current) {\n      gridIdRef.current = apiRef.current.getGridId();\n    }\n  }, []);\n  const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return {\n      height: \"100%\",\n      ...props.containerStyle || {}\n    };\n  }, [props.containerStyle]);\n  const processWhenReady = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((func) => {\n    if (ready.current && !frameworkOverridesRef.current?.shouldQueueUpdates()) {\n      func();\n    } else {\n      whenReadyFuncs.current.push(func);\n    }\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const changes = extractGridPropertyChanges(prevProps.current, props);\n    prevProps.current = props;\n    processWhenReady(() => {\n      if (apiRef.current) {\n        (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._processOnChange)(changes, apiRef.current);\n      }\n    });\n  }, [props]);\n  const renderMode = !react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore || (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getGridOption)(props, \"renderingMode\") === \"legacy\" ? \"legacy\" : \"default\";\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style, className: props.className, ref: setRef2 }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(RenderModeContext.Provider, { value: renderMode }, context && !context.isDestroyed() ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(gridComp_default, { context }) : null, portalManager.current?.getPortals() ?? null));\n};\nfunction extractGridPropertyChanges(prevProps, nextProps) {\n  const changes = {};\n  Object.keys(nextProps).forEach((propKey) => {\n    if (excludeReactCompProps.has(propKey)) {\n      if (deprecatedReactCompProps.has(propKey)) {\n        (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._warn)(274, { prop: propKey });\n      }\n      return;\n    }\n    const propValue = nextProps[propKey];\n    if (prevProps[propKey] !== propValue) {\n      changes[propKey] = propValue;\n    }\n  });\n  return changes;\n}\nvar ReactFrameworkComponentWrapper = class extends ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.BaseComponentWrapper {\n  constructor(parent, gridOptions) {\n    super();\n    this.parent = parent;\n    this.gridOptions = gridOptions;\n  }\n  createWrapper(UserReactComponent, componentType) {\n    const gridOptions = this.gridOptions;\n    const reactiveCustomComponents = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getGridOption)(gridOptions, \"reactiveCustomComponents\");\n    if (reactiveCustomComponents) {\n      const getComponentClass = (propertyName) => {\n        switch (propertyName) {\n          case \"filter\":\n            return (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getGridOption)(gridOptions, \"enableFilterHandlers\") ? FilterDisplayComponentWrapper : FilterComponentWrapper;\n          case \"floatingFilterComponent\":\n            return (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getGridOption)(gridOptions, \"enableFilterHandlers\") ? FloatingFilterDisplayComponentWrapper : FloatingFilterComponentWrapper;\n          case \"dateComponent\":\n            return DateComponentWrapper;\n          case \"dragAndDropImageComponent\":\n            return DragAndDropImageComponentWrapper;\n          case \"loadingOverlayComponent\":\n            return LoadingOverlayComponentWrapper;\n          case \"noRowsOverlayComponent\":\n            return NoRowsOverlayComponentWrapper;\n          case \"statusPanel\":\n            return StatusPanelComponentWrapper;\n          case \"toolPanel\":\n            return ToolPanelComponentWrapper;\n          case \"menuItem\":\n            return MenuItemComponentWrapper;\n          case \"cellRenderer\":\n            return CellRendererComponentWrapper;\n          case \"innerHeaderComponent\":\n            return InnerHeaderComponentWrapper;\n        }\n      };\n      const ComponentClass = getComponentClass(componentType.name);\n      if (ComponentClass) {\n        return new ComponentClass(UserReactComponent, this.parent, componentType);\n      }\n    } else {\n      switch (componentType.name) {\n        case \"filter\":\n        case \"floatingFilterComponent\":\n        case \"dateComponent\":\n        case \"dragAndDropImageComponent\":\n        case \"loadingOverlayComponent\":\n        case \"noRowsOverlayComponent\":\n        case \"statusPanel\":\n        case \"toolPanel\":\n        case \"menuItem\":\n        case \"cellRenderer\":\n          warnReactiveCustomComponents();\n          break;\n      }\n    }\n    const suppressFallbackMethods = !componentType.cellRenderer && componentType.name !== \"toolPanel\";\n    return new ReactComponent(UserReactComponent, this.parent, componentType, suppressFallbackMethods);\n  }\n};\nvar DetailCellRenderer = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { registry, context, gos, rowModel } = beans;\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [gridCssClasses, setGridCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [detailGridOptions, setDetailGridOptions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [detailRowData, setDetailRowData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const ctrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGuiRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const resizeObserverDestroyFunc = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const parentModules = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getGridRegisteredModules)(props.api.getGridId(), detailGridOptions?.rowModelType ?? \"clientSide\"),\n    [props]\n  );\n  const topClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssClasses.toString() + \" ag-details-row\", [cssClasses]);\n  const gridClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => gridCssClasses.toString() + \" ag-details-grid\", [gridCssClasses]);\n  if (ref) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => ({\n      refresh() {\n        return ctrlRef.current?.refresh() ?? false;\n      }\n    }));\n  }\n  if (props.template) {\n    (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._warn)(230);\n  }\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGuiRef.current = eRef;\n    if (!eRef) {\n      ctrlRef.current = context.destroyBean(ctrlRef.current);\n      resizeObserverDestroyFunc.current?.();\n      return;\n    }\n    const compProxy = {\n      toggleCss: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      toggleDetailGridCss: (name, on) => setGridCssClasses((prev) => prev.setClass(name, on)),\n      setDetailGrid: (gridOptions) => setDetailGridOptions(gridOptions),\n      setRowData: (rowData) => setDetailRowData(rowData),\n      getGui: () => eGuiRef.current\n    };\n    const ctrl = registry.createDynamicBean(\"detailCellRendererCtrl\", true);\n    if (!ctrl) {\n      return;\n    }\n    context.createBean(ctrl);\n    ctrl.init(compProxy, props);\n    ctrlRef.current = ctrl;\n    if (gos.get(\"detailRowAutoHeight\")) {\n      const checkRowSizeFunc = () => {\n        if (eGuiRef.current == null) {\n          return;\n        }\n        const clientHeight = eGuiRef.current.clientHeight;\n        if (clientHeight != null && clientHeight > 0) {\n          const updateRowHeightFunc = () => {\n            props.node.setRowHeight(clientHeight);\n            if ((0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._isClientSideRowModel)(gos, rowModel) || (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._isServerSideRowModel)(gos, rowModel)) {\n              rowModel.onRowHeightChanged();\n            }\n          };\n          setTimeout(updateRowHeightFunc, 0);\n        }\n      };\n      resizeObserverDestroyFunc.current = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._observeResize)(beans, eRef, checkRowSizeFunc);\n      checkRowSizeFunc();\n    }\n  }, []);\n  const registerGridApi = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((api) => {\n    ctrlRef.current?.registerDetailWithMaster(api);\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: topClassName, ref: setRef2 }, detailGridOptions && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    AgGridReactUi,\n    {\n      className: gridClassName,\n      ...detailGridOptions,\n      modules: parentModules,\n      rowData: detailRowData,\n      passGridApi: registerGridApi\n    }\n  ));\n});\nvar ReactFrameworkOverrides = class extends ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.VanillaFrameworkOverrides {\n  constructor(processQueuedUpdates) {\n    super(\"react\");\n    this.processQueuedUpdates = processQueuedUpdates;\n    this.queueUpdates = false;\n    this.renderingEngine = \"react\";\n    this.frameworkComponents = {\n      agGroupCellRenderer: groupCellRenderer_default,\n      agGroupRowRenderer: groupCellRenderer_default,\n      agDetailCellRenderer: DetailCellRenderer\n    };\n    this.wrapIncoming = (callback, source) => {\n      if (source === \"ensureVisible\") {\n        return runWithoutFlushSync(callback);\n      }\n      return callback();\n    };\n  }\n  frameworkComponent(name) {\n    return this.frameworkComponents[name];\n  }\n  isFrameworkComponent(comp) {\n    if (!comp) {\n      return false;\n    }\n    const prototype = comp.prototype;\n    const isJsComp = prototype && \"getGui\" in prototype;\n    return !isJsComp;\n  }\n  getLockOnRefresh() {\n    this.queueUpdates = true;\n  }\n  releaseLockOnRefresh() {\n    this.queueUpdates = false;\n    this.processQueuedUpdates();\n  }\n  shouldQueueUpdates() {\n    return this.queueUpdates;\n  }\n  runWhenReadyAsync() {\n    return isReact19();\n  }\n};\n\n// packages/ag-grid-react/src/agGridReact.tsx\nvar AgGridReact = class extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor() {\n    super(...arguments);\n    this.apiListeners = [];\n    this.setGridApi = (api) => {\n      this.api = api;\n      this.apiListeners.forEach((listener) => listener(api));\n    };\n  }\n  registerApiListener(listener) {\n    this.apiListeners.push(listener);\n  }\n  componentWillUnmount() {\n    this.apiListeners.length = 0;\n  }\n  render() {\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(AgGridReactUi, { ...this.props, passGridApi: this.setGridApi });\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/interfaces.ts\n\nfunction useGridCustomComponent(methods) {\n  const { setMethods } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(CustomContext);\n  setMethods(methods);\n}\nfunction useGridCellEditor(callbacks) {\n  useGridCustomComponent(callbacks);\n}\nfunction useGridDate(callbacks) {\n  return useGridCustomComponent(callbacks);\n}\nfunction useGridFilter(callbacks) {\n  return useGridCustomComponent(callbacks);\n}\nfunction useGridFilterDisplay(callbacks) {\n  return useGridCustomComponent(callbacks);\n}\nfunction useGridFloatingFilter(callbacks) {\n  useGridCustomComponent(callbacks);\n}\nfunction useGridMenuItem(callbacks) {\n  useGridCustomComponent(callbacks);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ag-grid-react/dist/package/index.esm.mjs\n");

/***/ })

};
;
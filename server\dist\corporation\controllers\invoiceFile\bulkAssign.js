"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bulkAssignInvoiceFiles = void 0;
const helpers_1 = require("../../../utils/helpers");
const bulkAssignInvoiceFiles = async (req, res) => {
    try {
        const { fileIds, assignedTo, updatedBy } = req.body;
        if (!Array.isArray(fileIds) || fileIds.length === 0 || !assignedTo) {
            return res.status(400).json({
                success: false,
                message: "fileIds and assignedTo are required",
            });
        }
        const updated = await prisma.invoiceFile.updateMany({
            where: {
                id: { in: fileIds },
                deletedAt: null,
            },
            data: {
                assignedTo,
                updatedBy,
                updatedAt: new Date(),
            },
        });
        return res.status(200).json({
            success: true,
            message: `Successfully assigned ${updated.count} invoice file(s)`,
            data: updated,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.bulkAssignInvoiceFiles = bulkAssignInvoiceFiles;
//# sourceMappingURL=bulkAssign.js.map
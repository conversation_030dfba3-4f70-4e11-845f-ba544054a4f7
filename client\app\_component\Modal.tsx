// components/CustomModal.tsx
import React from "react";

interface CustomModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

export function CustomModal({ isOpen, onClose, children }: CustomModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
      <div className="relative w-full max-w-[425px] bg-background rounded-lg shadow-lg p-6">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-muted-foreground hover:text-muted-foreground/70"
          aria-label="Close"
        >
          ✕
        </button>
        {children}
      </div>
    </div>
  );
}
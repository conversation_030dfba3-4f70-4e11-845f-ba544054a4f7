import { Router } from "express";
import { checkUniqueInvoiceFile, createInvoiceFile, createBulkInvoiceFiles } from "../../controllers/invoiceFile/create";
import { viewInvoiceFile } from "../../controllers/invoiceFile/view";
import { viewAllInvoiceFiles } from "../../controllers/invoiceFile/viewAll";
import { viewInvoiceFilesByUser } from "../../controllers/invoiceFile/viewByUser";
import { updateInvoiceFile } from "../../controllers/invoiceFile/update";
import { deleteInvoiceFile } from "../../controllers/invoiceFile/delete";
import { authenticate } from "../../../middleware/authentication";
import { bulkAssignInvoiceFiles } from "../../controllers/invoiceFile/bulkAssign";

const router = Router();

router.post(
  "/bulk",
  // authenticate,
  createBulkInvoiceFiles
);
// Create new invoice file entry
router.get(
  "/check-unique",
  // authenticate,
  checkUniqueInvoiceFile
);

router.post(
  "/bulk-assign",
  // authenticate,
  bulkAssignInvoiceFiles
);  

// Create single invoice file
router.post("/", authenticate, createInvoiceFile);

// Get single invoice file by ID
router.get(
  "/:id",
  // authenticate,
  viewInvoiceFile
);

// Get all invoice files with pagination
router.get(
  "/",
  // authenticate,
  viewAllInvoiceFiles
);

// Get invoice files assigned to specific user
router.get(
  "/user/:userId",
  // authenticate,
  viewInvoiceFilesByUser
);

// Update invoice file by ID
router.put(
  "/:id",
  // authenticate,
  updateInvoiceFile
);

// Soft delete invoice file by ID
router.delete(
  "/:id",
  // authenticate,
  deleteInvoiceFile
);

export default router;

"use client";

import { format } from "date-fns";
import {
  FileText,
  Calendar,
  Hash,
  User,
  Clock,
  Edit,
  Trash2,
  X,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { InvoiceFile } from "../types";

// Define your modal component
function Modal({ isOpen, onClose, children }: { isOpen: boolean; onClose: () => void; children: React.ReactNode }) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative max-w-lg w-full max-h-[80vh] overflow-y-auto bg-background p-6 rounded-lg shadow-lg">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-muted-foreground hover:text-muted-foreground/70"
          aria-label="Close"
        >
          <X className="h-4 w-4" />
        </button>
        {children}
      </div>
    </div>
  );
}

interface InvoiceFileDetailsProps {
  file: InvoiceFile | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit: (file: InvoiceFile) => void;
  onDelete: (file: InvoiceFile) => void;
}

export function InvoiceFileDetails({
  file,
  open,
  onOpenChange,
  onEdit,
  onDelete,
}: InvoiceFileDetailsProps) {
  if (!file) return null;

  const isDeleted = !!file.deletedAt;

  const handleClose = () => onOpenChange(false);

  return (
    <Modal isOpen={open} onClose={handleClose}>
      <div className="sm:max-w-[650px] max-h-[150vh]">
        {/* Header */}
        <div className="flex flex-row items-center justify-between mb-4 overflow-hidden">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span className="font-semibold text-lg">Invoice File Details</span>
          </div>
        </div>

        {/* Status Badge and Actions */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Badge
              variant={isDeleted ? "destructive" : "default"}
              className="text-sm"
            >
              {isDeleted ? "Deleted" : "Active"}
            </Badge>
            {!isDeleted && (
              <div className="flex space-x-2">
                <Button size="sm" onClick={() => { handleClose(); onEdit(file); }}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => { handleClose(); onDelete(file); }}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </Button>
              </div>
            )}
          </div>

          {/* File Information Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">File Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* File Name */}
                <div className="flex items-center space-x-3">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">File Name</p>
                    <p className="text-sm text-muted-foreground">{file.fileName}</p>
                  </div>
                </div>
                {/* Date */}
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Date</p>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(file.date), "MMMM dd, yyyy")}
                    </p>
                  </div>
                </div>
                {/* Number of Pages */}
                <div className="flex items-center space-x-3">
                  <Hash className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Number of Pages</p>
                    <p className="text-sm text-muted-foreground">{file.noOfPages}</p>
                  </div>
                </div>
                {/* Assigned To */}
                <div className="flex items-center space-x-3">
                  <User className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Assigned To</p>
                    {file.assignedToUser ? (
                      <div className="flex items-center space-x-2 mt-1">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={file.assignedToUser.avatar} />
                          <AvatarFallback className="text-xs">
                            {file.assignedToUser.firstName[0]}
                            {file.assignedToUser.lastName[0]}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm text-muted-foreground">
                          {file.assignedToUser.firstName} {file.assignedToUser.lastName}
                        </span>
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">Unassigned</p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Audit Trail Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Audit Trail</CardTitle>
              <CardDescription>
                Track of all changes made to this invoice file
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 overflow-hidden">
              {/* Created */}
              <div className="flex items-start space-x-3">
                <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
                  <Clock className="h-4 w-4 text-green-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">Created</p>
                    <p className="text-xs text-muted-foreground">
                      {format(new Date(file.createdAt), "MMM dd, yyyy 'at' h:mm a")}
                    </p>
                  </div>
                  {file.createdByUser && (
                    <p className="text-sm text-muted-foreground">
                      by {file.createdByUser.firstName} {file.createdByUser.lastName}
                    </p>
                  )}
                </div>
              </div>

              {/* Last Updated */}
              {file.updatedAt && (
                <>
                  <hr className="my-2" />
                  <div className="flex items-start space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                      <Edit className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">Last Updated</p>
                        <p className="text-xs text-muted-foreground">
                          {format(new Date(file.updatedAt), "MMM dd, yyyy 'at' h:mm a")}
                        </p>
                      </div>
                      {file.updatedByUser && (
                        <p className="text-sm text-muted-foreground">
                          by {file.updatedByUser.firstName} {file.updatedByUser.lastName}
                        </p>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* Deleted */}
              {file.deletedAt && (
                <>
                  <hr className="my-2" />
                  <div className="flex items-start space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-red-100 rounded-full">
                      <Trash2 className="h-4 w-4 text-red-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">Deleted</p>
                        <p className="text-xs text-muted-foreground">
                          {format(new Date(file.deletedAt), "MMM dd, yyyy 'at' h:mm a")}
                        </p>
                      </div>
                      {file.deletedByUser && (
                        <p className="text-sm text-muted-foreground">
                          by {file.deletedByUser.firstName} {file.deletedByUser.lastName}
                        </p>
                      )}
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </Modal>
  );
}
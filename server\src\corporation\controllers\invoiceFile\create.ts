import { handleError } from "../../../utils/helpers";
import { createItem } from "../../../utils/operation";

export const createInvoiceFile = async (req: any, res: any) => {
  try {
    const {carrier, date, fileName, noOfPages, assignedTo, createdBy } = req.body;    

    // Validation
    if (!carrier || !date || !fileName || !noOfPages) {
      return res.status(400).json({
        success: false,
        message: "Carrier, Date, file name, and number of pages are required",
      });
    }

    if (typeof noOfPages !== "number" || noOfPages <= 0) {
      return res.status(400).json({
        success: false,
        message: "Number of pages must be a positive number",
      });
    }

    // Prepare fields with default assigned_to = created_by if not provided
    const fields = {
      carrierId: Number(carrier),
      date: new Date(date),
      fileName: fileName.trim(),
      noOfPages: Number(noOfPages),
      assignedTo: assignedTo || createdBy,
      createdBy: createdBy,
    };

    await createItem({
      model: "invoiceFile",
      fieldName: "id",
      fields: fields,
      res: res,
      req: req,
      successMessage: "Invoice file entry has been created successfully",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

export const createBulkInvoiceFiles = async (req: any, res: any) => {
  try {
    const { carrier, date, createdBy, files } = req.body;

    // Validate shared fields
    if (!carrier || !date || !Array.isArray(files) || files.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Carrier, date, and a non-empty files array are required",
      });
    }

    const formattedData = [];

    for (const file of files) {
      const { fileName, noOfPages, assignedTo } = file;

      if (!fileName || typeof noOfPages !== "number" || noOfPages <= 0) {
        return res.status(400).json({
          success: false,
          message: "Each file must have a valid fileName and a positive number of pages",
        });
      }

      formattedData.push({
        carrierId: Number(carrier),
        date: new Date(date),
        fileName: fileName.trim(),
        noOfPages: Number(noOfPages),
        assignedTo: Number(assignedTo || createdBy),
        createdBy: Number(createdBy),
      });
    }

    const existingFiles = await prisma.invoiceFile.findMany({
      where: {
        OR: formattedData.map((file) => ({
          carrierId: file.carrierId,
          noOfPages: file.noOfPages,
          date: file.date,
          fileName: file.fileName,
        })),
      },
      select: {
        fileName: true,
        noOfPages: true,
        date: true,
      },
    });

    if (existingFiles.length > 0) {
      const conflictList = existingFiles.map((file) =>
        `File "${file.fileName}" with ${file.noOfPages} pages on ${file.date.toISOString().split('T')[0]}`
      );

      return res.status(409).json({
        success: false,
        message: `Duplicate file(s) already exist for the same carrier, date, and no. of pages.`,
        duplicates: conflictList,
      });
    }

    const created = await prisma.invoiceFile.createMany({
      data: formattedData,
    });

    return res.status(201).json({
      success: true,
      message: `${created.count} invoice file(s) created successfully`,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};


export const checkUniqueInvoiceFile = async (req: any, res: any) => {
  try {
    const { carrier, date, fileName, noOfPages } = req.body;

    if (!carrier || !date || !fileName || !noOfPages) {
      return res.status(400).json({ message: "Missing required fields" });
    }

    const existing = await prisma.invoiceFile.findFirst({
      where: {
        carrierId: carrier,
        date: new Date(date),
        fileName,
        noOfPages,
      },
    });

    if (existing) {
      return res.status(409).json({
        message: "Invoice file with the same carrier, date, file name, and page count already exists.",
      });
    }

    return res.status(200).json({ message: "Unique. You may proceed." });
  } catch (error) {
    return handleError(res, error);
  }
};

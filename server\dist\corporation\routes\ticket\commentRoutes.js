"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/ticket/comment/create");
const view_1 = require("../../controllers/ticket/comment/view");
const update_1 = require("../../controllers/ticket/comment/update");
const delete_1 = require("../../controllers/ticket/comment/delete");
const router = (0, express_1.Router)();
// Create a new comment
router.post("/", 
//authenticate,
create_1.createComment);
// Get all comments
router.get("/", 
//authenticate,
view_1.getAllComments);
// Get comments by ticket ID
router.get("/ticket/:ticketId", 
//authenticate,
view_1.getCommentsByTicketId);
// Update a comment
router.put("/:id", 
//authenticate,
update_1.updateComment);
// Delete a comment (soft delete)
router.delete("/:id", 
//authenticate,
delete_1.deleteComment);
exports.default = router;
//# sourceMappingURL=commentRoutes.js.map
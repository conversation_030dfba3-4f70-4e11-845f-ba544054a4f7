"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserClients = void 0;
const getUserClients = async (req, res) => {
    const userId = Number(req.query.userId);
    if (!userId) {
        return res.status(400).json({ success: false, error: "User ID is required" });
    }
    try {
        const userWithClients = await prisma.user.findUnique({
            where: { id: userId },
            include: {
                userClients: {
                    include: {
                        client: true,
                    },
                },
            },
        });
        return res.status(200).json({ success: true, data: userWithClients?.userClients.map(uc => uc.client) || [] });
    }
    catch (error) {
        console.error("Error fetching clients:", error);
        return res.status(500).json({ success: false, error: "Failed to get clients" });
    }
};
exports.getUserClients = getUserClients;
//# sourceMappingURL=view.js.map
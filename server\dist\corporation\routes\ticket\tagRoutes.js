"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/ticket/tag/create");
const view_1 = require("../../controllers/ticket/tag/view");
const update_1 = require("../../controllers/ticket/tag/update");
const delete_1 = require("../../controllers/ticket/tag/delete");
const assign_1 = require("../../controllers/ticket/tag/assign");
const router = (0, express_1.Router)();
// Create a new tag
router.post("/", 
//authenticate,
create_1.createTag);
// Get all tags
router.get("/", 
//authenticate,
view_1.getAllTags);
// Get tags by ticket ID
router.get("/ticket/:ticketId", 
//authenticate,
view_1.getTagsByTicketId);
// Update a tag
router.put("/:id", 
//authenticate,
update_1.updateTag);
// Delete a tag (soft delete)
router.delete("/:id", 
//authenticate,
delete_1.deleteTag);
// Assign tags to a ticket
router.post("/assign", 
//authenticate,
assign_1.assignTagsToTicket);
exports.default = router;
//# sourceMappingURL=tagRoutes.js.map
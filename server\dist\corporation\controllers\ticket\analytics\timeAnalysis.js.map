{"version": 3, "file": "timeAnalysis.js", "sourceRoot": "", "sources": ["../../../../../src/corporation/controllers/ticket/analytics/timeAnalysis.ts"], "names": [], "mappings": ";;;AAAA,uDAAwD;AAExD;;;;;;;GAOG;AACI,MAAM,qBAAqB,GAAG,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IAChE,IAAI,CAAC;QACH,MAAM,EACJ,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,UAAU,EACV,OAAO,EACP,QAAQ,GACT,GAAG,GAAG,CAAC,KAAK,CAAC;QAEd,iCAAiC;QACjC,MAAM,iBAAiB,GAAQ;YAC7B,SAAS,EAAE,IAAI;SAChB,CAAC;QAEF,qBAAqB;QACrB,IAAI,QAAQ,IAAI,MAAM,EAAE,CAAC;YACvB,iBAAiB,CAAC,SAAS,GAAG,EAAE,CAAC;YACjC,IAAI,QAAQ,EAAE,CAAC;gBACb,iBAAiB,CAAC,SAAS,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvD,CAAC;YACD,IAAI,MAAM,EAAE,CAAC;gBACX,iBAAiB,CAAC,SAAS,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YAClE,iBAAiB,CAAC,IAAI,GAAG;gBACvB,OAAO,EAAE,QAAQ;aAClB,CAAC;QACJ,CAAC;QAED,yBAAyB;QACzB,IAAI,QAAQ,EAAE,CAAC;YACb,iBAAiB,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACxC,CAAC;QAED,uCAAuC;QACvC,MAAM,gBAAgB,GAAQ,EAAE,CAAC;QACjC,IAAI,UAAU,EAAE,CAAC;YACf,gBAAgB,CAAC,UAAU,GAAG,UAAU,CAAC;QAC3C,CAAC;QAED,0BAA0B;QAC1B,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC3C,KAAK,EAAE;gBACL,GAAG,iBAAiB;gBACpB,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI;oBAC9C,MAAM,EAAE;wBACN,IAAI,EAAE,gBAAgB;qBACvB;iBACF,CAAC;aACH;YACD,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,OAAO,EAAE;wBACP,aAAa,EAAE,IAAI;qBACpB;oBACD,OAAO,EAAE;wBACP,SAAS,EAAE,KAAK;qBACjB;iBACF;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;yBAC1B;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,iCAAiC;QACjC,IAAI,eAAe,GAAU,EAAE,CAAC;QAEhC,IAAI,OAAO,EAAE,CAAC;YACZ,yBAAyB;YACzB,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;gBAC1D,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;aACvB,CAAC,CAAC;YACH,IAAI,aAAa,EAAE,CAAC;gBAClB,eAAe,GAAG,CAAC,aAAa,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,kCAAkC;YAClC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAe,CAAC;YACzC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACvB,IAAI,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;oBAC5B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;wBACrC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;oBACjC,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YACH,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,mCAAmC;QACnC,MAAM,iBAAiB,GAAG,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACpD,MAAM,YAAY,GAAa,EAAE,CAAC;YAElC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACvB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM;oBAAE,OAAO;gBAErC,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC/C,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAClE,CAAC;gBAEF,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC5E,IAAI,CAAC,UAAU;oBAAE,OAAO;gBAExB,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC5E,MAAM,cAAc,GAAG,UAAU,GAAG,CAAC,CAAC;gBAEtC,IAAI,OAAa,CAAC;gBAElB,IAAI,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnD,uCAAuC;oBACvC,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC;oBAC9D,MAAM,cAAc,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,KAAK,WAAW,CAAC,CAAC;oBAEnF,IAAI,cAAc,EAAE,CAAC;wBACnB,OAAO,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;oBAC/C,CAAC;yBAAM,CAAC;wBACN,wCAAwC;wBACxC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC,CAAC,eAAe;oBACvC,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,0BAA0B;oBAC1B,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC,CAAC,kDAAkD;gBAC1E,CAAC;gBAED,MAAM,aAAa,GAAG,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;gBACnF,MAAM,gBAAgB,GAAG,aAAa,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;gBAE1D,IAAI,gBAAgB,IAAI,CAAC,EAAE,CAAC,CAAC,uBAAuB;oBAClD,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,uBAAuB;YACvB,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC;YACvC,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,OAAO,GAAG,CAAC,CAAC;YAChB,IAAI,OAAO,GAAG,CAAC,CAAC;YAEhB,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;gBACnB,oBAAoB;gBACpB,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC;gBAE7E,mBAAmB;gBACnB,MAAM,WAAW,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACpD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;oBACjC,UAAU,GAAG,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;gBACvE,CAAC;qBAAM,CAAC;oBACN,UAAU,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;gBACrC,CAAC;gBAED,cAAc;gBACd,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;gBACpC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;YACtC,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,SAAS,EAAE,KAAK,CAAC,IAAI,IAAI,eAAe;gBACxC,UAAU,EAAE,KAAK,CAAC,KAAK;gBACvB,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,GAAG;gBAChD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;gBAC9C,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG;gBACxC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG;gBACxC,QAAQ,EAAE,OAAO;gBACjB,UAAU;aACX,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;QAE9D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,iBAAiB;SACxB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACxD,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AA/LW,QAAA,qBAAqB,yBA+LhC"}
// types/formTypes.ts

export type CustomField = {
  id: string;
  name: string;
  type?: string;
  value?: string;
};

export type FormEntry = {
  company: string;
  division: string;
  invoice: string;
  masterInvoice?: string;
  bol?: string;
  invoiceDate: string;
  receivedDate: string;
  shipmentDate?: string;
  carrierName: string;
  invoiceStatus: string;
  manualMatching: string;
  invoiceType: string;
  billToClient?: string;
  finalInvoice?: boolean;
  currency: string;
  qtyShipped?: string;
  weightUnitName?: string;
  quantityBilledText?: string;
  freightClass?: string;
  invoiceTotal: string;
  savings?: string;
  financialNotes?: string;
  ftpFileName: string;
  invoiceFileId?: string;
  ftpPage: string;
  docAvailable?: string[];
  otherDocuments?: string;
  notes?: string;
  legrandAlias?: string;
  legrandCompanyName?: string;
  legrandAddress?: string;
  legrandZipcode?: string;
  shipperAlias?: string;
  shipperAddress?: string;
  shipperZipcode?: string;
  consigneeAlias?: string;
  consigneeAddress?: string;
  consigneeZipcode?: string;
  billtoAlias?: string;
  billtoAddress?: string;
  billtoZipcode?: string;
  shipperType: string;
  consigneeType: string;
  billtoType: string;
  legrandFreightTerms: string;
  customFields?: CustomField[];
  enteredBy?: string;
};

export type FormValues = {
  associateId: string;
  clientId: string;
  entries: FormEntry[];
};

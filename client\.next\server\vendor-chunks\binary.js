/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/binary";
exports.ids = ["vendor-chunks/binary"];
exports.modules = {

/***/ "(ssr)/./node_modules/binary/index.js":
/*!**************************************!*\
  !*** ./node_modules/binary/index.js ***!
  \**************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("var Chainsaw = __webpack_require__(/*! chainsaw */ \"(ssr)/./node_modules/chainsaw/index.js\");\nvar EventEmitter = (__webpack_require__(/*! events */ \"events\").EventEmitter);\nvar Buffers = __webpack_require__(/*! buffers */ \"(ssr)/./node_modules/buffers/index.js\");\nvar Vars = __webpack_require__(/*! ./lib/vars.js */ \"(ssr)/./node_modules/binary/lib/vars.js\");\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream);\n\nexports = module.exports = function (bufOrEm, eventName) {\n    if (Buffer.isBuffer(bufOrEm)) {\n        return exports.parse(bufOrEm);\n    }\n    \n    var s = exports.stream();\n    if (bufOrEm && bufOrEm.pipe) {\n        bufOrEm.pipe(s);\n    }\n    else if (bufOrEm) {\n        bufOrEm.on(eventName || 'data', function (buf) {\n            s.write(buf);\n        });\n        \n        bufOrEm.on('end', function () {\n            s.end();\n        });\n    }\n    return s;\n};\n\nexports.stream = function (input) {\n    if (input) return exports.apply(null, arguments);\n    \n    var pending = null;\n    function getBytes (bytes, cb, skip) {\n        pending = {\n            bytes : bytes,\n            skip : skip,\n            cb : function (buf) {\n                pending = null;\n                cb(buf);\n            },\n        };\n        dispatch();\n    }\n    \n    var offset = null;\n    function dispatch () {\n        if (!pending) {\n            if (caughtEnd) done = true;\n            return;\n        }\n        if (typeof pending === 'function') {\n            pending();\n        }\n        else {\n            var bytes = offset + pending.bytes;\n            \n            if (buffers.length >= bytes) {\n                var buf;\n                if (offset == null) {\n                    buf = buffers.splice(0, bytes);\n                    if (!pending.skip) {\n                        buf = buf.slice();\n                    }\n                }\n                else {\n                    if (!pending.skip) {\n                        buf = buffers.slice(offset, bytes);\n                    }\n                    offset = bytes;\n                }\n                \n                if (pending.skip) {\n                    pending.cb();\n                }\n                else {\n                    pending.cb(buf);\n                }\n            }\n        }\n    }\n    \n    function builder (saw) {\n        function next () { if (!done) saw.next() }\n        \n        var self = words(function (bytes, cb) {\n            return function (name) {\n                getBytes(bytes, function (buf) {\n                    vars.set(name, cb(buf));\n                    next();\n                });\n            };\n        });\n        \n        self.tap = function (cb) {\n            saw.nest(cb, vars.store);\n        };\n        \n        self.into = function (key, cb) {\n            if (!vars.get(key)) vars.set(key, {});\n            var parent = vars;\n            vars = Vars(parent.get(key));\n            \n            saw.nest(function () {\n                cb.apply(this, arguments);\n                this.tap(function () {\n                    vars = parent;\n                });\n            }, vars.store);\n        };\n        \n        self.flush = function () {\n            vars.store = {};\n            next();\n        };\n        \n        self.loop = function (cb) {\n            var end = false;\n            \n            saw.nest(false, function loop () {\n                this.vars = vars.store;\n                cb.call(this, function () {\n                    end = true;\n                    next();\n                }, vars.store);\n                this.tap(function () {\n                    if (end) saw.next()\n                    else loop.call(this)\n                }.bind(this));\n            }, vars.store);\n        };\n        \n        self.buffer = function (name, bytes) {\n            if (typeof bytes === 'string') {\n                bytes = vars.get(bytes);\n            }\n            \n            getBytes(bytes, function (buf) {\n                vars.set(name, buf);\n                next();\n            });\n        };\n        \n        self.skip = function (bytes) {\n            if (typeof bytes === 'string') {\n                bytes = vars.get(bytes);\n            }\n            \n            getBytes(bytes, function () {\n                next();\n            });\n        };\n        \n        self.scan = function find (name, search) {\n            if (typeof search === 'string') {\n                search = new Buffer(search);\n            }\n            else if (!Buffer.isBuffer(search)) {\n                throw new Error('search must be a Buffer or a string');\n            }\n            \n            var taken = 0;\n            pending = function () {\n                var pos = buffers.indexOf(search, offset + taken);\n                var i = pos-offset-taken;\n                if (pos !== -1) {\n                    pending = null;\n                    if (offset != null) {\n                        vars.set(\n                            name,\n                            buffers.slice(offset, offset + taken + i)\n                        );\n                        offset += taken + i + search.length;\n                    }\n                    else {\n                        vars.set(\n                            name,\n                            buffers.slice(0, taken + i)\n                        );\n                        buffers.splice(0, taken + i + search.length);\n                    }\n                    next();\n                    dispatch();\n                } else {\n                    i = Math.max(buffers.length - search.length - offset - taken, 0);\n\t\t\t\t}\n                taken += i;\n            };\n            dispatch();\n        };\n        \n        self.peek = function (cb) {\n            offset = 0;\n            saw.nest(function () {\n                cb.call(this, vars.store);\n                this.tap(function () {\n                    offset = null;\n                });\n            });\n        };\n        \n        return self;\n    };\n    \n    var stream = Chainsaw.light(builder);\n    stream.writable = true;\n    \n    var buffers = Buffers();\n    \n    stream.write = function (buf) {\n        buffers.push(buf);\n        dispatch();\n    };\n    \n    var vars = Vars();\n    \n    var done = false, caughtEnd = false;\n    stream.end = function () {\n        caughtEnd = true;\n    };\n    \n    stream.pipe = Stream.prototype.pipe;\n    Object.getOwnPropertyNames(EventEmitter.prototype).forEach(function (name) {\n        stream[name] = EventEmitter.prototype[name];\n    });\n    \n    return stream;\n};\n\nexports.parse = function parse (buffer) {\n    var self = words(function (bytes, cb) {\n        return function (name) {\n            if (offset + bytes <= buffer.length) {\n                var buf = buffer.slice(offset, offset + bytes);\n                offset += bytes;\n                vars.set(name, cb(buf));\n            }\n            else {\n                vars.set(name, null);\n            }\n            return self;\n        };\n    });\n    \n    var offset = 0;\n    var vars = Vars();\n    self.vars = vars.store;\n    \n    self.tap = function (cb) {\n        cb.call(self, vars.store);\n        return self;\n    };\n    \n    self.into = function (key, cb) {\n        if (!vars.get(key)) {\n            vars.set(key, {});\n        }\n        var parent = vars;\n        vars = Vars(parent.get(key));\n        cb.call(self, vars.store);\n        vars = parent;\n        return self;\n    };\n    \n    self.loop = function (cb) {\n        var end = false;\n        var ender = function () { end = true };\n        while (end === false) {\n            cb.call(self, ender, vars.store);\n        }\n        return self;\n    };\n    \n    self.buffer = function (name, size) {\n        if (typeof size === 'string') {\n            size = vars.get(size);\n        }\n        var buf = buffer.slice(offset, Math.min(buffer.length, offset + size));\n        offset += size;\n        vars.set(name, buf);\n        \n        return self;\n    };\n    \n    self.skip = function (bytes) {\n        if (typeof bytes === 'string') {\n            bytes = vars.get(bytes);\n        }\n        offset += bytes;\n        \n        return self;\n    };\n    \n    self.scan = function (name, search) {\n        if (typeof search === 'string') {\n            search = new Buffer(search);\n        }\n        else if (!Buffer.isBuffer(search)) {\n            throw new Error('search must be a Buffer or a string');\n        }\n        vars.set(name, null);\n        \n        // simple but slow string search\n        for (var i = 0; i + offset <= buffer.length - search.length + 1; i++) {\n            for (\n                var j = 0;\n                j < search.length && buffer[offset+i+j] === search[j];\n                j++\n            );\n            if (j === search.length) break;\n        }\n        \n        vars.set(name, buffer.slice(offset, offset + i));\n        offset += i + search.length;\n        return self;\n    };\n    \n    self.peek = function (cb) {\n        var was = offset;\n        cb.call(self, vars.store);\n        offset = was;\n        return self;\n    };\n    \n    self.flush = function () {\n        vars.store = {};\n        return self;\n    };\n    \n    self.eof = function () {\n        return offset >= buffer.length;\n    };\n    \n    return self;\n};\n\n// convert byte strings to unsigned little endian numbers\nfunction decodeLEu (bytes) {\n    var acc = 0;\n    for (var i = 0; i < bytes.length; i++) {\n        acc += Math.pow(256,i) * bytes[i];\n    }\n    return acc;\n}\n\n// convert byte strings to unsigned big endian numbers\nfunction decodeBEu (bytes) {\n    var acc = 0;\n    for (var i = 0; i < bytes.length; i++) {\n        acc += Math.pow(256, bytes.length - i - 1) * bytes[i];\n    }\n    return acc;\n}\n\n// convert byte strings to signed big endian numbers\nfunction decodeBEs (bytes) {\n    var val = decodeBEu(bytes);\n    if ((bytes[0] & 0x80) == 0x80) {\n        val -= Math.pow(256, bytes.length);\n    }\n    return val;\n}\n\n// convert byte strings to signed little endian numbers\nfunction decodeLEs (bytes) {\n    var val = decodeLEu(bytes);\n    if ((bytes[bytes.length - 1] & 0x80) == 0x80) {\n        val -= Math.pow(256, bytes.length);\n    }\n    return val;\n}\n\nfunction words (decode) {\n    var self = {};\n    \n    [ 1, 2, 4, 8 ].forEach(function (bytes) {\n        var bits = bytes * 8;\n        \n        self['word' + bits + 'le']\n        = self['word' + bits + 'lu']\n        = decode(bytes, decodeLEu);\n        \n        self['word' + bits + 'ls']\n        = decode(bytes, decodeLEs);\n        \n        self['word' + bits + 'be']\n        = self['word' + bits + 'bu']\n        = decode(bytes, decodeBEu);\n        \n        self['word' + bits + 'bs']\n        = decode(bytes, decodeBEs);\n    });\n    \n    // word8be(n) == word8le(n) for all n\n    self.word8 = self.word8u = self.word8be;\n    self.word8s = self.word8bs;\n    \n    return self;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/binary/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/binary/lib/vars.js":
/*!*****************************************!*\
  !*** ./node_modules/binary/lib/vars.js ***!
  \*****************************************/
/***/ ((module) => {

eval("module.exports = function (store) {\n    function getset (name, value) {\n        var node = vars.store;\n        var keys = name.split('.');\n        keys.slice(0,-1).forEach(function (k) {\n            if (node[k] === undefined) node[k] = {};\n            node = node[k]\n        });\n        var key = keys[keys.length - 1];\n        if (arguments.length == 1) {\n            return node[key];\n        }\n        else {\n            return node[key] = value;\n        }\n    }\n    \n    var vars = {\n        get : function (name) {\n            return getset(name);\n        },\n        set : function (name, value) {\n            return getset(name, value);\n        },\n        store : store || {},\n    };\n    return vars;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYmluYXJ5L2xpYi92YXJzLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNULDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbGllbnQvLi9ub2RlX21vZHVsZXMvYmluYXJ5L2xpYi92YXJzLmpzPzFlNjgiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoc3RvcmUpIHtcbiAgICBmdW5jdGlvbiBnZXRzZXQgKG5hbWUsIHZhbHVlKSB7XG4gICAgICAgIHZhciBub2RlID0gdmFycy5zdG9yZTtcbiAgICAgICAgdmFyIGtleXMgPSBuYW1lLnNwbGl0KCcuJyk7XG4gICAgICAgIGtleXMuc2xpY2UoMCwtMSkuZm9yRWFjaChmdW5jdGlvbiAoaykge1xuICAgICAgICAgICAgaWYgKG5vZGVba10gPT09IHVuZGVmaW5lZCkgbm9kZVtrXSA9IHt9O1xuICAgICAgICAgICAgbm9kZSA9IG5vZGVba11cbiAgICAgICAgfSk7XG4gICAgICAgIHZhciBrZXkgPSBrZXlzW2tleXMubGVuZ3RoIC0gMV07XG4gICAgICAgIGlmIChhcmd1bWVudHMubGVuZ3RoID09IDEpIHtcbiAgICAgICAgICAgIHJldHVybiBub2RlW2tleV07XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gbm9kZVtrZXldID0gdmFsdWU7XG4gICAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgdmFyIHZhcnMgPSB7XG4gICAgICAgIGdldCA6IGZ1bmN0aW9uIChuYW1lKSB7XG4gICAgICAgICAgICByZXR1cm4gZ2V0c2V0KG5hbWUpO1xuICAgICAgICB9LFxuICAgICAgICBzZXQgOiBmdW5jdGlvbiAobmFtZSwgdmFsdWUpIHtcbiAgICAgICAgICAgIHJldHVybiBnZXRzZXQobmFtZSwgdmFsdWUpO1xuICAgICAgICB9LFxuICAgICAgICBzdG9yZSA6IHN0b3JlIHx8IHt9LFxuICAgIH07XG4gICAgcmV0dXJuIHZhcnM7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/binary/lib/vars.js\n");

/***/ })

};
;
import { handleError } from "../../../utils/helpers";

export const bulkAssignInvoiceFiles = async (req: any, res: any) => {
  try {
    const { fileIds, assignedTo, updatedBy } = req.body;

    if (!Array.isArray(fileIds) || fileIds.length === 0 || !assignedTo) {
      return res.status(400).json({
        success: false,
        message: "fileIds and assignedTo are required",
      });
    }

    const updated = await prisma.invoiceFile.updateMany({
      where: {
        id: { in: fileIds },
        deletedAt: null,
      },
      data: {
        assignedTo,
        updatedBy,
        updatedAt: new Date(),
      },
    });

    return res.status(200).json({
      success: true,
      message: `Successfully assigned ${updated.count} invoice file(s)`,
      data: updated,
    });
  } catch (error) {
    return handleError(res, error);
  }
};

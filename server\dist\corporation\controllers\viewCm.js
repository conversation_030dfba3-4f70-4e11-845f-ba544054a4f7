"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserCm = void 0;
const helpers_1 = require("../../utils/helpers");
const getUserCm = async (req, res) => {
    try {
        const userId = Number(req.params.id);
        if (!userId)
            return res.status(400).json({ message: "User id required" });
        // Check if the user is already level 4
        const user = await prisma.user.findUnique({ where: { id: userId } });
        if (user.level === 4) {
            return res.status(200).json({ cm: user });
        }
        // Recursive function to find CM (level 4)
        async function findCM(id) {
            const u = await prisma.user.findUnique({ where: { id } });
            if (!u)
                return null;
            if (u.level === 4)
                return u;
            if (!u.parent_id)
                return null;
            return findCM(u.parent_id);
        }
        const cm = await findCM(user.parent_id);
        if (cm) {
            return res.status(200).json({ cm: { id: cm.id, username: cm.username } });
        }
        else {
            return res.status(404).json({ message: "CM not found" });
        }
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getUserCm = getUserCm;
//# sourceMappingURL=viewCm.js.map